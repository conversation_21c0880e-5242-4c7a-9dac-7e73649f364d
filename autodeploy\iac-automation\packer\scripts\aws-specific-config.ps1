# AWS-Specific Configuration
# This script applies AWS-specific configurations for optimal EC2 performance

Write-Host "Applying AWS-specific configurations..."

# Configure EC2Launch or EC2Config settings
Write-Host "Configuring EC2 Launch settings..."

$ec2LaunchConfigPath = "C:\ProgramData\Amazon\EC2-Windows\Launch\Config\LaunchConfig.json"
$ec2ConfigPath = "C:\Program Files\Amazon\Ec2ConfigService\Settings\Config.xml"

if (Test-Path $ec2LaunchConfigPath) {
    Write-Host "Configuring EC2Launch..."
    try {
        $launchConfig = Get-Content $ec2LaunchConfigPath | ConvertFrom-Json
        
        # Enable administrator account
        $launchConfig.adminPasswordType = "Random"
        $launchConfig.adminPassword = ""
        
        # Set computer name
        $launchConfig.setComputerName = $true
        
        # Set wallpaper
        $launchConfig.setWallpaper = $true
        
        # Add DNS suffix search list
        $launchConfig.addDnsSuffixList = $true
        
        # Extend boot volume
        $launchConfig.extendBootVolumeSize = $true
        
        # Handle user data
        $launchConfig.handleUserData = $true
        
        $launchConfig | ConvertTo-Json -Depth 10 | Set-Content $ec2LaunchConfigPath
        Write-Host "EC2Launch configured successfully."
    } catch {
        Write-Host "Failed to configure EC2Launch: $($_.Exception.Message)"
    }
} elseif (Test-Path $ec2ConfigPath) {
    Write-Host "Configuring EC2Config..."
    try {
        [xml]$configXml = Get-Content $ec2ConfigPath
        
        # Enable plugins
        $plugins = $configXml.Ec2ConfigurationSettings.Plugins.Plugin
        foreach ($plugin in $plugins) {
            switch ($plugin.Name) {
                "Ec2SetPassword" { $plugin.State = "Enabled" }
                "Ec2SetComputerName" { $plugin.State = "Enabled" }
                "Ec2InitializeDrives" { $plugin.State = "Enabled" }
                "Ec2EventLog" { $plugin.State = "Enabled" }
                "Ec2ConfigureRDP" { $plugin.State = "Enabled" }
                "Ec2OutputRDPCert" { $plugin.State = "Enabled" }
                "Ec2SetDriveLetter" { $plugin.State = "Enabled" }
                "Ec2WindowsActivate" { $plugin.State = "Enabled" }
                "Ec2DynamicBootVolumeSize" { $plugin.State = "Enabled" }
            }
        }
        
        $configXml.Save($ec2ConfigPath)
        Write-Host "EC2Config configured successfully."
    } catch {
        Write-Host "Failed to configure EC2Config: $($_.Exception.Message)"
    }
}

# Configure CloudWatch Agent
Write-Host "Configuring CloudWatch Agent..."
try {
    $cloudWatchConfig = @{
        "agent" = @{
            "metrics_collection_interval" = 60
            "run_as_user" = "cwagent"
        }
        "metrics" = @{
            "namespace" = "CWAgent"
            "metrics_collected" = @{
                "cpu" = @{
                    "measurement" = @("cpu_usage_idle", "cpu_usage_iowait", "cpu_usage_user", "cpu_usage_system")
                    "metrics_collection_interval" = 60
                    "totalcpu" = $false
                }
                "disk" = @{
                    "measurement" = @("used_percent")
                    "metrics_collection_interval" = 60
                    "resources" = @("*")
                }
                "diskio" = @{
                    "measurement" = @("io_time")
                    "metrics_collection_interval" = 60
                    "resources" = @("*")
                }
                "mem" = @{
                    "measurement" = @("mem_used_percent")
                    "metrics_collection_interval" = 60
                }
                "swap" = @{
                    "measurement" = @("swap_used_percent")
                    "metrics_collection_interval" = 60
                }
            }
        }
        "logs" = @{
            "logs_collected" = @{
                "windows_events" = @{
                    "collect_list" = @(
                        @{
                            "event_name" = "System"
                            "event_levels" = @("ERROR", "WARNING")
                            "log_group_name" = "windows-system"
                            "log_stream_name" = "{instance_id}-system"
                        },
                        @{
                            "event_name" = "Application"
                            "event_levels" = @("ERROR", "WARNING")
                            "log_group_name" = "windows-application"
                            "log_stream_name" = "{instance_id}-application"
                        }
                    )
                }
            }
        }
    }
    
    $configPath = "C:\ProgramData\Amazon\AmazonCloudWatchAgent\amazon-cloudwatch-agent.json"
    $cloudWatchConfig | ConvertTo-Json -Depth 10 | Set-Content $configPath
    
    Write-Host "CloudWatch Agent configuration created."
} catch {
    Write-Host "Failed to configure CloudWatch Agent: $($_.Exception.Message)"
}

# Configure AWS Systems Manager Agent
Write-Host "Configuring SSM Agent..."
try {
    # Ensure SSM Agent is set to start automatically
    Set-Service -Name "AmazonSSMAgent" -StartupType Automatic
    
    # Start the service if it's not running
    $ssmService = Get-Service -Name "AmazonSSMAgent" -ErrorAction SilentlyContinue
    if ($ssmService -and $ssmService.Status -ne "Running") {
        Start-Service -Name "AmazonSSMAgent"
    }
    
    Write-Host "SSM Agent configured successfully."
} catch {
    Write-Host "Failed to configure SSM Agent: $($_.Exception.Message)"
}

# Configure EBS optimization settings
Write-Host "Configuring EBS optimization settings..."
try {
    # Set disk timeout values for EBS
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Disk" -Name "TimeOutValue" -Value 60
    
    # Configure storage spaces for better performance
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\StorageSpaces" -Name "SpacesMigrationEnabled" -Value 1
    
    Write-Host "EBS optimization settings configured."
} catch {
    Write-Host "Failed to configure EBS settings: $($_.Exception.Message)"
}

# Configure network settings for AWS
Write-Host "Configuring network settings for AWS..."
try {
    # Disable IPv6 (often not needed in AWS)
    New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters" -Name "DisabledComponents" -Value 0xffffffff -PropertyType DWord -Force
    
    # Configure TCP settings for better performance
    netsh int tcp set global autotuninglevel=normal
    netsh int tcp set global chimney=enabled
    netsh int tcp set global rss=enabled
    netsh int tcp set global netdma=enabled
    
    Write-Host "Network settings configured for AWS."
} catch {
    Write-Host "Failed to configure network settings: $($_.Exception.Message)"
}

# Configure Windows Time Service for AWS
Write-Host "Configuring Windows Time Service..."
try {
    # Configure NTP servers for AWS
    w32tm /config /manualpeerlist:"***************" /syncfromflags:manual /reliable:yes /update
    
    # Start Windows Time service
    Set-Service -Name "w32time" -StartupType Automatic
    Start-Service -Name "w32time"
    
    # Force time sync
    w32tm /resync /force
    
    Write-Host "Windows Time Service configured for AWS."
} catch {
    Write-Host "Failed to configure Windows Time Service: $($_.Exception.Message)"
}

# Configure power settings for AWS
Write-Host "Configuring power settings..."
try {
    # Set power plan to High Performance
    powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
    
    # Disable hibernation
    powercfg /hibernate off
    
    # Disable sleep
    powercfg /change standby-timeout-ac 0
    powercfg /change standby-timeout-dc 0
    
    Write-Host "Power settings configured."
} catch {
    Write-Host "Failed to configure power settings: $($_.Exception.Message)"
}

# Create startup script for instance initialization
Write-Host "Creating instance initialization script..."
try {
    $initScript = @'
# Instance Initialization Script
# This script runs on first boot to perform instance-specific configuration

Write-Host "Running instance initialization..."

# Get instance metadata
try {
    $instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id" -TimeoutSec 10
    $instanceType = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-type" -TimeoutSec 10
    $availabilityZone = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/availability-zone" -TimeoutSec 10
    
    Write-Host "Instance ID: $instanceId"
    Write-Host "Instance Type: $instanceType"
    Write-Host "Availability Zone: $availabilityZone"
} catch {
    Write-Host "Could not retrieve instance metadata"
}

# Configure computer name based on instance ID (if desired)
# Uncomment the following lines if you want to set computer name to instance ID
# if ($instanceId) {
#     $newName = "AWS-$($instanceId.Substring($instanceId.Length - 8))"
#     Rename-Computer -NewName $newName -Force
# }

Write-Host "Instance initialization completed."
'@
    
    $initScript | Out-File -FilePath "C:\Scripts\instance-init.ps1" -Encoding UTF8
    
    # Create the Scripts directory if it doesn't exist
    New-Item -ItemType Directory -Path "C:\Scripts" -Force
    
    Write-Host "Instance initialization script created."
} catch {
    Write-Host "Failed to create initialization script: $($_.Exception.Message)"
}

Write-Host "AWS-specific configuration completed."
