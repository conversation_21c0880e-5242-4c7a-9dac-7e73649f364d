[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$VMName,
    
    [Parameter(Mandatory=$true)]
    [string]$OrgUnit,
    
    [Parameter(Mandatory=$false)]
    [string]$DomainController = $env:TF_VAR_ad_domain_controller
)

if (-not $DomainController) {
    throw "✗ DomainController parameter is not set and TF_VAR_ad_domain_controller environment variable is missing."
}

Write-Host "=== Active Directory Validation for $VMName ===" -ForegroundColor Green
Write-Verbose "=== Active Directory Validation for $VMName ==="
try {
    # Actual AD check for existing computer object
    $existingObject = Get-ADComputer -Filter "Name -eq '$VMName'" -Server $DomainController -ErrorAction SilentlyContinue
    # In real implementation, check AD for existing computer object
    # $existingObject = Get-ADComputer -Filter "Name -eq '$VMName'" -Server $DomainController -ErrorAction SilentlyContinue
    
    # Simulate check
    $existingObject = $null
    
    if ($existingObject) {
        throw "✗ Computer object $VMName already exists in Active Directory"
    } else {
        Write-Host "✓ Computer object $VMName does not exist - safe to proceed" -ForegroundColor Green
    }
        Write-Verbose "✓ Computer object $VMName does not exist - safe to proceed"
    # Validate OU exists
    Write-Host "Validating organizational unit: $OrgUnit" -ForegroundColor Yellow
    
    Write-Verbose "Validating organizational unit: $OrgUnit"
    # $ouExists = Get-ADOrganizationalUnit -Filter "DistinguishedName -eq '$OrgUnit'" -Server $DomainController -ErrorAction SilentlyContinue
    
    # Actual OU validation
    $ouObject = Get-ADOrganizationalUnit -Filter "DistinguishedName -eq '$OrgUnit'" -Server $DomainController -ErrorAction SilentlyContinue
    $ouExists = $null -ne $ouObject
    
        Write-Host "✓ Organizational Unit validated successfully" -ForegroundColor Green
        return
        exit 0
        Write-Verbose "✓ Organizational Unit validated successfully"
        exit 0
    }
}
    throw "✗ Active Directory validation failed: $($_.Exception.Message)"
    exit 1
}