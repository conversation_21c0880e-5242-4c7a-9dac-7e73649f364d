# Build VMware Templates <PERSON>rip<PERSON>
# This script builds both Windows Server 2019 and 2022 VMware templates

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("2019", "2022", "both")]
    [string]$Version = "both",

    [Parameter(Mandatory=$false)]
    [string]$VarFile = "variables/vmware.pkrvars.hcl",

    [Parameter(Mandatory=$false)]
    [switch]$DebugMode
)

Write-Host "Building VMware Windows Server Templates" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Check if Packer is installed
try {
    $packerVersion = packer version
    Write-Host "Packer version: $packerVersion" -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: Packer is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check if variable file exists
if (-not (Test-Path $VarFile)) {
    Write-Host "ERROR: Variable file '$VarFile' not found" -ForegroundColor Red
    Write-Host "Please create the variable file or specify a different one with -VarFile parameter" -ForegroundColor Yellow
    exit 1
}

# Set Packer log level if debug is enabled
if ($DebugMode) {
    $env:PACKER_LOG = "1"
    Write-Host "Debug logging enabled" -ForegroundColor Yellow
}

# Function to build a template
function Build-Template {
    param(
        [string]$TemplatePath,
        [string]$VersionName
    )
    
    Write-Host "`nBuilding Windows Server $VersionName VMware template..." -ForegroundColor Yellow
    Write-Host "Template: $TemplatePath" -ForegroundColor Cyan
    Write-Host "Variables: $VarFile" -ForegroundColor Cyan
    
    $startTime = Get-Date
    
    try {
        # Validate the template first
        Write-Host "Validating template..." -ForegroundColor Cyan
        packer validate -var-file="$VarFile" "$TemplatePath"
        
        if ($LASTEXITCODE -ne 0) {
            throw "Template validation failed"
        }
        
        Write-Host "Template validation successful" -ForegroundColor Green
        
        # Build the template
        Write-Host "Starting build..." -ForegroundColor Cyan
        packer build -var-file="$VarFile" "$TemplatePath"
        
        if ($LASTEXITCODE -ne 0) {
            throw "Template build failed"
        }
        
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        Write-Host "Windows Server $VersionName VMware template built successfully!" -ForegroundColor Green
        Write-Host "Build duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor Cyan
        
    } catch {
        Write-Host "ERROR: Failed to build Windows Server $VersionName template: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Build templates based on version parameter
$buildResults = @()

if ($Version -eq "2019" -or $Version -eq "both") {
    $result = Build-Template -TemplatePath "windows-server-2019/vmware.pkr.hcl" -VersionName "2019"
    $buildResults += @{ Version = "2019"; Success = $result }
}

if ($Version -eq "2022" -or $Version -eq "both") {
    $result = Build-Template -TemplatePath "windows-server-2022/vmware.pkr.hcl" -VersionName "2022"
    $buildResults += @{ Version = "2022"; Success = $result }
}

# Summary
Write-Host "`n" -ForegroundColor Green
Write-Host "Build Summary" -ForegroundColor Green
Write-Host "=============" -ForegroundColor Green

$successCount = 0
$totalCount = $buildResults.Count

foreach ($result in $buildResults) {
    $status = if ($result.Success) { "SUCCESS" } else { "FAILED" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    
    Write-Host "Windows Server $($result.Version): $status" -ForegroundColor $color
    
    if ($result.Success) {
        $successCount++
    }
}

Write-Host "`nTotal: $successCount/$totalCount builds successful" -ForegroundColor Cyan

if ($successCount -eq $totalCount) {
    Write-Host "All builds completed successfully!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "Some builds failed. Check the output above for details." -ForegroundColor Red
    exit 1
}
