services:
  terraform:
    build:
      context: .
      dockerfile: tf-pwsh.Dockerfile
      args:
        POWERSHELL_VERSION: ${POWERSHELL_VERSION}
        TERRAFORM_VERSION: ${TERRAFORM_VERSION}
        TF_VAR_infrastructure_spec_file: ${TF_VAR_infrastructure_spec_file}
        TF_VAR_guest_id: ${TF_VAR_guest_id}
        TF_VAR_vsphere_server: ${TF_VAR_vsphere_server}
        TF_VAR_allow_unverified_ssl: ${TF_VAR_allow_unverified_ssl}
    container_name: pwsh-terraform
    command: ["/bin/bash", "-c", "while true; do sleep 3600; done"]
    working_dir: /
    volumes:
      - ./output:/output
    env_file:
      - "/tmp/dev.env"
    networks:
      - app-network

  node-red:
    image: nodered/node-red:latest
    container_name: node-red
    ports:
      - "1880:1880"
    volumes:
      - node_red_data:/data
    networks:
      - app-network

  # Windmill workflow automation platform
  windmill-db:
    image: postgres:14
    container_name: windmill-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=windmill
      - POSTGRES_DB=windmill
    networks:
      - app-network

  windmill:
    image: ghcr.io/windmill-labs/windmill:main
    container_name: windmill
    restart: unless-stopped
    ports:
      - "8080:8001"
    environment:
      - DATABASE_URL=***********************************************/windmill?sslmode=disable
      - RUST_LOG=info
      - BASE_URL=http://localhost:8001
      - DISABLE_NUSER=true
    depends_on:
      - windmill-db
    volumes:
      - windmill_worker_data:/tmp/windmill
      - /var/run/docker.sock:/var/run/docker.sock
      - ./scripts:/windmill-scripts:ro
    networks:
      - app-network

  # Windmill worker for running jobs
  windmill-worker:
    image: ghcr.io/windmill-labs/windmill:main
    container_name: windmill-worker
    restart: unless-stopped
    environment:
      - DATABASE_URL=***********************************************/windmill?sslmode=disable
      - MODE=worker
      - WORKER_GROUP=default
    depends_on:
      - windmill-db
      - windmill
    volumes:
      - windmill_worker_data:/tmp/windmill
      - /var/run/docker.sock:/var/run/docker.sock
      - ./scripts:/windmill-scripts:ro
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  node_red_data:
  windmill_db_data:
  windmill_worker_data:
