services:
  terraform:
    build:
      context: .
      dockerfile: tf-pwsh.Dockerfile
      args:
        POWERSHELL_VERSION: ${POWERSHELL_VERSION}
        TERRAFORM_VERSION: ${TERRAFORM_VERSION}
        TF_VAR_infrastructure_spec_file: ${TF_VAR_infrastructure_spec_file}
        TF_VAR_guest_id: ${TF_VAR_guest_id}
        TF_VAR_vsphere_server: ${TF_VAR_vsphere_server}
        TF_VAR_allow_unverified_ssl: ${TF_VAR_allow_unverified_ssl}
    container_name: pwsh-terraform
    command: ["/bin/bash", "-c", "while true; do sleep 3600; done"]
    working_dir: /
    volumes:
      - ./output:/output
    env_file:
      - "/tmp/dev.env"
    networks:
      - app-network

  node-red:
    image: nodered/node-red:latest
    container_name: node-red
    ports:
      - "1880:1880"
    volumes:
      - node_red_data:/data
    networks:
      - app-network

  # Windmill workflow automation platform
  windmill-db:
    image: postgres:14
    container_name: windmill-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=windmill
      - POSTGRES_DB=windmill
    networks:
      - app-network

  windmill:
    image: ghcr.io/windmill-labs/windmill:main
    container_name: windmill
    restart: unless-stopped
    ports:
      - "8080:8001"
    environment:
      - DATABASE_URL=***********************************************/windmill?sslmode=disable
      - RUST_LOG=info
      - BASE_URL=http://localhost:8001
      - DISABLE_NUSER=true
    depends_on:
      - windmill-db
    volumes:
      - windmill_worker_data:/tmp/windmill
      - /var/run/docker.sock:/var/run/docker.sock
      - ./scripts:/windmill-scripts:ro
    networks:
      - app-network

  # Windmill worker for running jobs
  windmill-worker:
    image: ghcr.io/windmill-labs/windmill:main
    container_name: windmill-worker
    restart: unless-stopped
    environment:
      - DATABASE_URL=***********************************************/windmill?sslmode=disable
      - MODE=worker
      - WORKER_GROUP=default
    depends_on:
      - windmill-db
      - windmill
    volumes:
      - windmill_worker_data:/tmp/windmill
      - /var/run/docker.sock:/var/run/docker.sock
      - ./scripts:/windmill-scripts:ro
    networks:
      - app-network

  # Packer builder service for Windows Server images
  packer-builder:
    build:
      context: .
      dockerfile: packer.Dockerfile
      args:
        - PACKER_VERSION=${PACKER_VERSION:-1.10.0}
        - TERRAFORM_VERSION=${TERRAFORM_VERSION:-1.6.6}
    container_name: packer-windows-builder
    hostname: packer-builder
    environment:
      # AWS Configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
      - AWS_PROFILE=${AWS_PROFILE:-default}

      # Azure Configuration (optional)
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
      - AZURE_SUBSCRIPTION_ID=${AZURE_SUBSCRIPTION_ID}

      # VMware vSphere Configuration
      - VCENTER_SERVER=${VCENTER_SERVER}
      - VCENTER_USERNAME=${VCENTER_USERNAME}
      - VCENTER_PASSWORD=${VCENTER_PASSWORD}
      - VCENTER_DATACENTER=${VCENTER_DATACENTER}
      - VCENTER_CLUSTER=${VCENTER_CLUSTER}
      - VCENTER_DATASTORE=${VCENTER_DATASTORE}
      - VCENTER_NETWORK=${VCENTER_NETWORK}

      # Packer Configuration
      - PACKER_LOG=${PACKER_LOG:-0}
      - PACKER_LOG_PATH=/workspace/logs/packer.log
      - PACKER_CACHE_DIR=/workspace/packer_cache

      # Build Configuration
      - BUILD_PLATFORM=${BUILD_PLATFORM:-both}
      - BUILD_VERSION=${BUILD_VERSION:-both}
      - BUILD_DEBUG=${BUILD_DEBUG:-false}
      - BUILD_PARALLEL=${BUILD_PARALLEL:-false}

      # System Configuration
      - TZ=${TZ:-UTC}
      - DEBIAN_FRONTEND=noninteractive

    volumes:
      # Mount the packer directory for development
      - ./packer:/workspace
      # Mount logs directory
      - ./packer/logs:/workspace/logs
      # Mount packer cache for faster builds
      - packer-cache:/workspace/packer_cache
      # Mount AWS credentials (alternative to environment variables)
      - ${HOME}/.aws:/home/<USER>/.aws:ro
      # Mount SSH keys for VMware access
      - ${HOME}/.ssh:/home/<USER>/.ssh:ro
      # Mount any ISO files
      - ${ISO_PATH:-./packer/ISO}:/workspace/ISO:ro
      # Mount Docker socket for potential nested container builds
      - /var/run/docker.sock:/var/run/docker.sock:ro

    working_dir: /workspace
    # Keep container running for interactive use
    tty: true
    stdin_open: true
    # Network configuration - use host network for VMware access
    network_mode: ${NETWORK_MODE:-host}
    # Resource limits
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-4G}
          cpus: ${CPU_LIMIT:-2.0}
        reservations:
          memory: ${MEMORY_RESERVATION:-2G}
          cpus: ${CPU_RESERVATION:-1.0}
    # Health check
    healthcheck:
      test: ["CMD", "packer", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # Restart policy
    restart: unless-stopped
    profiles:
      - packer

  # AWS-specific packer builder service
  packer-aws:
    extends: packer-builder
    container_name: packer-aws-builder
    environment:
      - BUILD_PLATFORM=aws
    command: ["bash", "-c", "echo 'AWS Packer Builder Ready. Run: docker compose exec packer-aws packer/scripts/build/build-aws.sh --help'"]
    profiles:
      - packer
      - aws-only

  # VMware-specific packer builder service
  packer-vmware:
    extends: packer-builder
    container_name: packer-vmware-builder
    environment:
      - BUILD_PLATFORM=vmware
    command: ["bash", "-c", "echo 'VMware Packer Builder Ready. Run: docker compose exec packer-vmware packer/scripts/build/build-vmware.sh --help'"]
    profiles:
      - packer
      - vmware-only

networks:
  app-network:
    driver: bridge

volumes:
  node_red_data:
  windmill_db_data:
  windmill_worker_data:
  packer-cache:
    driver: local
