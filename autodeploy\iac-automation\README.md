# iac-automation

## Overview

This project provides a local automation stack for infrastructure-as-code and workflow testing using Docker Compose.

- **Terraform** (containerized, for IaC)
- **Node-RED** (workflow automation/testing)
- **Windmill** (job/workflow runner)

---

## Prerequisites

- Docker & Docker Compose
- (Optional) Terraform CLI for local runs

---

## Project Structure

```text:
iac-automation/
├── docker-compose.yml
├── main.tf
├── variables.tf
├── outputs.tf
├── tf-pwsh.Dockerfile
├── TF_VAR_vsphere_credentials
├── zscaler_root.cer
├── LICENSE.txt
├── infrastructure/
│   ├── vm-spec.json
│   └── vm-spec_test.json
├── scripts/
│   └── pre-deploy/
│       ├── ad-validation.ps1
│       ├── connectivity-check.ps1
│       └── ipam-validation.ps1
├── output/
   └── (generated deployment summary, logs, etc.)
```

---

## Quick Start

 > This section is in-progress.

1. **Clone the repo**

   ```bash
   <NAME_EMAIL>:bcxslm/iac-automation.git
   cd iac-automation
   ```

2. **Configure environment**
   - Edit `.env` for Terraform and service variables (no secrets for dev/testing).
   - Place vSphere credentials in `TF_VAR_vsphere_credentials` (see `variables.tf` for required variables).

3. **Start the stack**

   ```bash
   docker compose up -d
   ```

4. **Check services**

   ```bash
   docker compose ps
   ```

5. **Run Terraform**

   ```bash
   docker exec -it pwsh-terraform bash
   terraform init
   terraform plan
   terraform apply
   # Output and deployment summary will be written to ./output/
   ```

---

## Service Endpoints

- **Node-RED**: <http://localhost:1880>
- **Windmill**: <http://localhost:8001>
- **Terraform**: via container shell

---

## Configuration

 > This section is in-progress.

- **VM specs**: Edit `infrastructure/vm-spec.json` (or `vm-spec_test.json` for test scenarios)
- **Terraform variables**: See `variables.tf` (override via `.env`, environment, or `TF_VAR_vsphere_credentials`)
- **Certificates**: `zscaler_root.cer` for proxy/SSL testing
- **Output**: Deployment summaries and logs are written to `output/` after `terraform apply`

---

## CI/CD

 > This section is in-progress.

- **Build & push Docker image**: See `.github/workflows/build-push-dev.yaml`
  - Uses secrets `DOCKERHUB_USERNAME` and `DOCKERHUB_TOKEN`
  - Tags image as `dev` and pushes to Docker Hub

---

## Cleaning Up

```bash
terraform destroy
docker compose down -v
```

---

## Troubleshooting

- **Containers not running**: `docker compose ps`
- **Service not ready**: Wait longer, check logs (`docker compose logs <service>`)
- **SSL errors**: `allow_unverified_ssl = true` is set in Terraform

---

## Notes

 > This section is in-progress.

- Use Node-RED and Windmill for workflow and job automation testing.
- For development/testing only. Do not use default credentials or self-signed certificates in production.
- The `output/` directory is auto-created by Terraform for deployment summaries and logs.
- Place vSphere credentials in `TF_VAR_vsphere_credentials` (not in source control).

---

## Credits

- [Node-RED](https://nodered.org/)
- [Windmill](https://github.com/windmill-labs/windmill)
- [Terraform](https://hashicorp.com)
