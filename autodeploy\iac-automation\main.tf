terraform {
  required_version = ">= 1.0"
  required_providers {
    vsphere = {
      source  = "vmware/vsphere"
      version = "~> 2.9"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 3.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.4"
    }
  }
}

# Load JSON infrastructure specification
locals {
  infrastructure_spec = jsondecode(file(var.infrastructure_spec_file))
  jobspecs            = local.infrastructure_spec.infrastructure.jobspecs

  # Flatten VM specifications with network configurations
  vm_deployments = flatten([
    for jobspec in local.jobspecs : [
      for i in range(jobspec.vm_configuration.vm_count) : {
        jobspec_id       = jobspec.jobspec_id
        vcenter_endpoint = jobspec.vcenter_endpoint
        datacenter       = jobspec.datacenter
        cluster          = jobspec.cluster
        vm_name          = "${jobspec.vm_configuration.vm_name}-${format("%02d", i + 1)}"
        memory           = jobspec.vm_configuration.vm_memory
        cpu              = jobspec.vm_configuration.vm_cpu
        disk_config      = jobspec.vm_configuration.disk_configuration
        network_config   = jobspec.vm_configuration.network_configuration
        vm_index         = i
      }
    ]
  ])

  # Map vm_key to deployment info for efficient lookup
  vm_deployment_map = {
    for vm in local.vm_deployments : "${vm.jobspec_id}-${vm.vm_name}" => vm
  }
}

# vSphere provider for live environment
provider "vsphere" {
  vsphere_server       = var.vsphere_server
  user                 = var.vsphere_user
  password             = var.vsphere_password
  allow_unverified_ssl = var.allow_unverified_ssl
}

# Data sources for each datacenter
resource "vsphere_datacenter" "dc" {
  for_each = { for js in local.jobspecs : js.jobspec_id => js }
  name     = each.value.datacenter
}

resource "vsphere_compute_cluster" "cluster" {
  for_each      = { for js in local.jobspecs : js.jobspec_id => js }
  name          = each.value.cluster
  datacenter_id = vsphere_datacenter.dc[each.key].id
  depends_on    = [vsphere_datacenter.dc]
}

# A local map to simplify the datastore for_each loop, only using known values
locals {
  datastore_map_keys = {
    for item in distinct(flatten([
      for js in local.jobspecs : [
        for disk in js.vm_configuration.disk_configuration : {
          key            = "${js.jobspec_id}-${disk.datastore}"
          jobspec_id     = js.jobspec_id
          datastore_name = disk.datastore
        }
      ]
    ])) : item.key => item
  }
}

# IMPORTANT: The host name must be manually provided.
# IMPORTANT: The datastore disk name must be manually provided here, and also requires an unpartitioned disk with a canonical name.

# Data source for an existing host system (required for creating VMFS datastore)
data "vsphere_host" "host" {
  for_each      = { for js in local.jobspecs : js.jobspec_id => js }
  name          = "DC0_HOST_0" # <--- REPLACE THIS WITH YOUR HOST NAME - NEEDS AN EXISTING HOST+DISK IN DEV ENV.
  datacenter_id = vsphere_datacenter.dc[each.key].id
}

# Data sources for datastores (unique combinations only)
resource "vsphere_vmfs_datastore" "datastores" {
  for_each = local.datastore_map_keys

  name           = each.value.datastore_name
  host_system_id = data.vsphere_host.host[each.value.jobspec_id].id
  disks          = ["DC0_DS_0"] # <--- REPLACE THIS WITH YOUR DISK NAME - NEEDS AN EXISTING HOST+DISK IN DEV ENV.
}

resource "vsphere_distributed_virtual_switch" "vds" {
  for_each        = { for js in local.jobspecs : js.jobspec_id => js }
  name            = "DC0_VDS_0"
  datacenter_id   = vsphere_datacenter.dc[each.key].id
  uplinks         = ["vds_uplink1", "vds_uplink2"]
  active_uplinks  = ["vds_uplink1"]
  standby_uplinks = ["vds_uplink2"]
  depends_on      = [vsphere_datacenter.dc]
}

# Data sources for networks
resource "vsphere_distributed_port_group" "primary_network" {
  for_each = { for js in local.jobspecs : js.jobspec_id => js }

  name                            = each.value.vm_configuration.network_configuration.primary_interface.port_group
  distributed_virtual_switch_uuid = vsphere_distributed_virtual_switch.vds[each.key].id
}

resource "vsphere_distributed_port_group" "secondary_network" {
  for_each = {
    for js in local.jobspecs : js.jobspec_id => js
    if can(js.vm_configuration.network_configuration.secondary_interface)
  }

  name                            = each.value.vm_configuration.network_configuration.secondary_interface.port_group
  distributed_virtual_switch_uuid = vsphere_distributed_virtual_switch.vds[each.key].id
}

# Simplified VM resource for debugging
resource "vsphere_virtual_machine" "vm" {
  for_each = { for vm in local.vm_deployments : "${vm.jobspec_id}-${vm.vm_name}" => vm }

  name             = each.value.vm_name
  resource_pool_id = vsphere_compute_cluster.cluster[each.value.jobspec_id].resource_pool_id
  datastore_id     = vsphere_vmfs_datastore.datastores["${each.value.jobspec_id}-${each.value.disk_config[0].datastore}"].id

  num_cpus = each.value.cpu
  memory   = each.value.memory
  guest_id = var.guest_id

  # Simplified - no firmware settings that might cause issues
  # firmware = "efi"
  # efi_secure_boot_enabled = false

  # Basic network interface
  network_interface {
    network_id = vsphere_distributed_port_group.primary_network[each.value.jobspec_id].id
  }

  # Single disk only for testing
  disk {
    label       = "disk0"
    size        = each.value.disk_config[0].size_gb
    unit_number = 0
  }

  # Remove hot-add settings that might cause device issues
  # cpu_hot_add_enabled    = true
  # memory_hot_add_enabled = true
}

# Create output directory
resource "null_resource" "create_output_directory" {
  provisioner "local-exec" {
    command     = <<-EOT
      if (!(Test-Path "${path.root}/output")) {
        New-Item -ItemType Directory -Force -Path "${path.root}/output"
      }
EOT
    interpreter = ["powershell", "-Command"]
    on_failure  = continue
  }
}

resource "local_file" "deployment_summary" {
  filename = "${path.root}/output/vm-deployment-summary.json"

  content = jsonencode({
    metadata           = local.infrastructure_spec.metadata
    deployment_time    = timestamp()
    total_vms_created  = length(local.vm_deployments)
    jobspecs_processed = length(local.jobspecs)
    resource_totals = {
      total_vcpus   = sum([for vm in local.vm_deployments : vm.cpu])
      total_vram_mb = sum([for vm in local.vm_deployments : vm.memory])
      total_disk_gb = sum(flatten([
        for vm in local.vm_deployments : [
          for disk in vm.disk_config : disk.size_gb
        ]
      ]))
    }
    created_vms = {
      for vm_key, vm in vsphere_virtual_machine.vm : vm_key => {
        name           = vm.name
        uuid           = vm.uuid
        power_state    = vm.power_state
        memory_mb      = vm.memory
        num_cpus       = vm.num_cpus
        guest_id       = vm.guest_id
        datacenter     = local.vm_deployment_map[vm_key].datacenter
        cluster        = local.vm_deployment_map[vm_key].cluster
        network_config = local.vm_deployment_map[vm_key].network_config
      }
    }
    next_steps = [
      "VMs created but powered off",
      "Install operating system manually or via automation",
      "Configure network settings as specified in vm-spec.json",
      "Power on VMs after OS installation"
    ]
  })

  depends_on = [
    vsphere_virtual_machine.vm,
    null_resource.create_output_directory
  ]
}