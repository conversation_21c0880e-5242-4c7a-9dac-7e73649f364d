FROM ubuntu:24.04@sha256:4f1db91d9560cf107b5832c0761364ec64f46777aa4ec637cca3008f287c975e

# Ubuntu base image: 24.04 @ SHA256:4f1db91d9560cf107b5832c0761364ec64f46777aa4ec637cca3008f287c975e
# Terraform binary: 12.0.2 (SHA256 Verified during build from source.)
# Powershell (Linux binary): 7.5.2 (SHA256 Verified during build from source.)

# Build arguments for non-sensitive data
ARG POWERSHELL_VERSION="7.5.2"
ARG TERRAFORM_VERSION="1.12.2"
ARG PACKER_VERSION="1.14.1"
ARG TF_VAR_infrastructure_spec_file="infrastructure/vm-spec_test.json"
ARG TF_VAR_guest_id="windows9Server64Guest"
ARG TF_VAR_vsphere_server="srv009972.mud.internal.co.za"
ARG TF_VAR_allow_unverified_ssl="true"

# Copy .env file if it exists (optional)
COPY .env /tmp/dev.env

WORKDIR /

COPY infrastructure/ ./infrastructure/
COPY output/ ./output/
COPY *.tf /
COPY zscaler_root.cer /usr/local/share/ca-certificates/zscaler_root.crt

# Set all environment variables
ENV POWERSHELL_VERSION=${POWERSHELL_VERSION} \
    TERRAFORM_VERSION=${TERRAFORM_VERSION} \
    PACKER_VERSION=${PACKER_VERSION} \
    TF_VAR_infrastructure_spec_file=${TF_VAR_infrastructure_spec_file} \
    TF_VAR_guest_id=${TF_VAR_guest_id} \
    TF_VAR_vsphere_server=${TF_VAR_vsphere_server} \
    TF_VAR_allow_unverified_ssl=${TF_VAR_allow_unverified_ssl} \
    DEBIAN_FRONTEND=noninteractive \
    AWS_CLI_VERSION=2.15.0

    # Might possibly be deprecated in favour of env vars, consider using them instead of the profile script.
RUN --mount=type=secret,id=TF_VAR_vsphere_credentials \
    if [ -f "/run/secrets/TF_VAR_vsphere_credentials" ]; then \
        echo "export TF_VAR_vsphere_credentials=$(cat /run/secrets/TF_VAR_vsphere_credentials)" >> /etc/profile.d/terraform_vars.sh; \
    else \
        echo "Warning: vsphere credentials secret not found"; \
    fi && \
    echo "export POWERSHELL_VERSION=${POWERSHELL_VERSION}" >> /etc/profile.d/terraform_vars.sh && \
    echo "export TERRAFORM_VERSION=${TERRAFORM_VERSION}" >> /etc/profile.d/terraform_vars.sh && \
    echo "export PACKER_VERSION=${PACKER_VERSION}" >> /etc/profile.d/terraform_vars.sh && \
    echo "export TF_VAR_infrastructure_spec_file=${TF_VAR_infrastructure_spec_file}" >> /etc/profile.d/terraform_vars.sh && \
    echo "export TF_VAR_guest_id=${TF_VAR_guest_id}" >> /etc/profile.d/terraform_vars.sh && \
    echo "export TF_VAR_vsphere_server=${TF_VAR_vsphere_server}" >> /etc/profile.d/terraform_vars.sh && \
    echo "export TF_VAR_allow_unverified_ssl=${TF_VAR_allow_unverified_ssl}" >> /etc/profile.d/terraform_vars.sh && \
    chmod +x /etc/profile.d/terraform_vars.sh && \
    exec /etc/profile.d/terraform_vars.sh

RUN apt-get update && \
    apt-get install -y ca-certificates wget curl jq unzip git python3 python3-pip openssh-client gnupg lsb-release && \
    update-ca-certificates && \
    ls -la /usr/local/share/ca-certificates/ && \
    ls -la /etc/ssl/certs/ | grep -i zscaler

# Install PowerShell
RUN mkdir -p /tmp/pwsh && cd /tmp/pwsh && \
    # Download PowerShell package and checksums
    wget -q "https://github.com/PowerShell/PowerShell/releases/download/v${POWERSHELL_VERSION}/powershell_${POWERSHELL_VERSION}-1.deb_amd64.deb" && \
    wget -q "https://github.com/PowerShell/PowerShell/releases/download/v${POWERSHELL_VERSION}/hashes.sha256" && \
    # Clean the hash file from BOM and null bytes
    tr -d '\000' < hashes.sha256 | sed 's/\xEF\xBB\xBF//g' > hashes.sha256.clean && \
    # Extract hash for our deb package
    grep "powershell_${POWERSHELL_VERSION}-1.deb_amd64.deb" hashes.sha256.clean | tee powershell.sha256 && \
    # Verify checksum
    sha256sum -c powershell.sha256 && \
    # Install PowerShell
    apt-get update && \
    apt-get install -y ./powershell_${POWERSHELL_VERSION}-1.deb_amd64.deb && \
    # Verify installation
    pwsh --version && \
    # Cleanup
    cd / && \
    rm -rf /tmp/pwsh

# Install Terraform
RUN wget -q "https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_SHA256SUMS" && \
    wget -q "https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip" && \
    grep "terraform_${TERRAFORM_VERSION}_linux_amd64.zip" terraform_${TERRAFORM_VERSION}_SHA256SUMS > terraform_SHA256SUM && \
    sha256sum --check terraform_SHA256SUM && \
    unzip -o terraform_${TERRAFORM_VERSION}_linux_amd64.zip && \
    mv terraform /usr/local/bin/ && \
    chmod +x /usr/local/bin/terraform && \
    rm terraform_${TERRAFORM_VERSION}_linux_amd64.zip terraform_${TERRAFORM_VERSION}_SHA256SUMS terraform_SHA256SUM

# Install Packer
RUN wget -q "https://releases.hashicorp.com/packer/${PACKER_VERSION}/packer_${PACKER_VERSION}_SHA256SUMS" && \
    wget -q "https://releases.hashicorp.com/packer/${PACKER_VERSION}/packer_${PACKER_VERSION}_linux_amd64.zip" && \
    grep "packer_${PACKER_VERSION}_linux_amd64.zip" packer_${PACKER_VERSION}_SHA256SUMS > packer_SHA256SUM && \
    sha256sum --check packer_SHA256SUM && \
    unzip -o packer_${PACKER_VERSION}_linux_amd64.zip && \
    mv packer /usr/local/bin/ && \
    chmod +x /usr/local/bin/packer && \
    rm packer_${PACKER_VERSION}_linux_amd64.zip packer_${PACKER_VERSION}_SHA256SUMS packer_SHA256SUM

# Install AWS CLI v2
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf aws awscliv2.zip

# Install Azure CLI (for potential Azure builds)
RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash

RUN terraform init

RUN terraform fmt -check -recursive ./infrastructure/
RUN terraform validate ./infrastructure/

# Verify all installations
RUN terraform --version && \
    packer version && \
    pwsh --version && \
    aws --version