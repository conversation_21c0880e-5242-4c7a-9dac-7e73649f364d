from datetime import datetime, date, time
import json
# FastAPI modules
from typing import Any
from fastapi import APIRouter, Request
from fastapi.responses import J<PERSON>NResponse
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel, Field
# from app.core.database import dbCheck

from app import logging
# from typing import date
from app.core.config import config

router = APIRouter()

restart_time = datetime.now()
last_restart = str(restart_time).split(".")[0]
# dbstatus = dbCheck()

class DbInfo(BaseModel):
    dbname: str = config.DB_NAME
    dbhost: str = config.DB_HOST
    dbport: str = config.DB_PORT

class Status(BaseModel):
    status: str | None = None
    message: str | None = None
    data: str | None = None
    success: bool

class SvcInfo(BaseModel):
    status: str | None = None
    message: str | None = None
    data: DbInfo | None = None
    success: bool


class stdResponse(BaseModel):
    status: str | None = None
    message: str | None = None
    data: str | None = None
    success: bool

@router.get("/health",summary="Api Service Status", tags=["service"], response_model=Status)
def hello_world() -> Any:
    """Say Hi to everyone!"""
    status = {
        'status'        : "Online",
        'message' : "",
        'data' : {
            'last_restart'  : last_restart,
            'api_status'    : "Configs Services is ready",
            'dc_connection' : config.DB_CONNECTION,
            'db_status'     : '',
            'uptime'        : str(datetime.now() - restart_time).split(".")[0],
            'online'        : True},
        'success' : True
    }
    logging.info("Service status requested")
    logging.info(status)
    # return {"message": "Hi, I'm alive!","status":"Online","success":True,"data" : ""}
    return JSONResponse(content=jsonable_encoder(status)) #return status #json.dumps()


@router.get("/info",summary="Api Service Information", tags=["service"], response_model=SvcInfo)
async def info():
    """Api Service Information"""
    logging.info("Service Information requested")
    svcinfo = {    
        "dbname": config.DB_NAME,
        "dbhost": config.DB_HOST,
        "dbport": config.DB_PORT
    }
    response = {"message": "Api Service Information","status":"Online","success":True,"data" : svcinfo, "errors": ""}
    logging.info(response)
    return JSONResponse(content=jsonable_encoder(response))
    # return 


# Test GET endpoint from worker tasks
@router.get("/test",summary="Test URL GET Params", tags=["configs"], response_model=stdResponse) # 
async def getTest(request: Request):
    params = request.query_params
    logging.info(f"URL Params received: {params}")
    response = params #ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# Test POST endpoint from worker tasks
@router.post("/test",summary="POST TEST Payload", tags=["configs"], response_model=stdResponse) # 
async def postTest(request: Request):
    """Return payload received in POST Body"""
    params = await request.json() #json.loads(request.data)
    logging.info(f"POST TEST payload received:\n{params}")    
    response={"success": True, "messages": "Test successful", "status": "OK", "data": params}
    resp_msg = (f"POST TEST payload Response:\n{response}")
    print(resp_msg)
    logging.warning(f"POST TEST payload Response:\n{resp_msg}")
    return JSONResponse(content=jsonable_encoder(response))
