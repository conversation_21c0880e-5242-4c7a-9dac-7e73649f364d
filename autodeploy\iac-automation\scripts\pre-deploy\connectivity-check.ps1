param(
    [Parameter(Mandatory=$true)]
    [string]$SpecFile
)

Write-Host "=== Pre-deployment Connectivity Check ===" -ForegroundColor Green

try {
    # Load the infrastructure specification
    $spec = Get-Content -Path $SpecFile | ConvertFrom-Json
    
    Write-Host "Checking vcsim connectivity..." -ForegroundColor Yellow
    
    # Test vcsim API endpoint
    $vcsimUrl = "https://localhost:8989/about"
    try {
                # Ignore SSL errors for self-signed certs (compatible with Windows PowerShell and PowerShell Core)
                add-type @"
        using System.Net;
        using System.Security.Cryptography.X509Certificates;
        public class TrustAllCertsPolicy : ICertificatePolicy {
            public bool CheckValidationResult(
                ServicePoint srvPoint, X509Certificate certificate,
                WebRequest request, int certificateProblem) {
                return true;
            }
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}" | Select-Object -Skip 1 | Where-Object { $_ -like "*vcsim*" }
        "@
                [System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy
        
                $response = Invoke-RestMethod -Uri $vcsimUrl -TimeoutSec 30
        Write-Host "✓ vcsim is accessible and responding" -ForegroundColor Green
    }
    catch {
        Write-Error "✗ [vcsim connectivity check] Failed to connect to vcsim: $($_.Exception.Message)"
        exit 1
    }
    
    # Validate Docker containers
    Write-Host "Checking Docker containers..." -ForegroundColor Yellow
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}" | Where-Object { $_ -like "*vcsim*" }
    
    if ($containers) {
        Write-Host "✓ vcsim container is running" -ForegroundColor Green
        Write-Host $containers
    } else {
        Write-Error "✗ [Docker container check] vcsim container is not running"
        exit 1
    }
    
    # Validate infrastructure specification
    Write-Host "Validating infrastructure specification..." -ForegroundColor Yellow
    
    if (-not $spec.infrastructure.virtual_machines) {
        Write-Error "✗ [Infrastructure specification validation] No virtual machines defined in specification"
        exit 1
    }
    
    $totalVMs = ($spec.infrastructure.virtual_machines | ForEach-Object { if ($_.vm_count) { $_.vm_count } else { 0 } } | Measure-Object -Sum).Sum
    Write-Host "✓ Infrastructure specification valid - $totalVMs VMs to deploy" -ForegroundColor Green
    
    Write-Host "=== Connectivity check completed successfully ===" -ForegroundColor Green
}
catch {
    Write-Error "✗ Connectivity check failed: $($_.Exception.Message)"
    Write-Error "✗ [General script error] Connectivity check failed: $($_.Exception.Message)"
    exit 1
