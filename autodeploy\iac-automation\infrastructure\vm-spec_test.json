{"metadata": {"name": "simple-test", "version": "1.0.0", "workflow_id": "WF-2025-001"}, "infrastructure": {"jobspecs": [{"jobspec_id": "JS-001", "vcenter_endpoint": "srv009972.mud.internal.co.za", "datacenter": "DC0", "cluster": "DC0_C0", "vm_configuration": {"vm_name": "test-vm", "vm_count": 1, "vm_memory": 2048, "vm_cpu": 2, "network_configuration": {"primary_interface": {"port_group": "VM Network"}}, "disk_configuration": [{"drive_letter": "C", "size_gb": 40, "datastore": "LocalDS_0", "type": "thin", "unit_number": 0}]}}]}}