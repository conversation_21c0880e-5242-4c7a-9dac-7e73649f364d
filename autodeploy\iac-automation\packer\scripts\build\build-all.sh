#!/bin/bash
# Build All Images Script
# This script builds Windows Server images for both VMware and AWS

set -euo pipefail

# Default values
PLATFORM="both"
VERSION="both"
DEBUG_MODE=false
PARALLEL=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -p, --platform PLATFORM Platform to build (vmware, aws, both) [default: both]"
    echo "  -v, --version VERSION    Version to build (2019, 2022, both) [default: both]"
    echo "  -d, --debug              Enable debug logging"
    echo "  -j, --parallel           Run builds in parallel"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                       # Build all platforms and versions"
    echo "  $0 -p vmware            # Build only VMware templates"
    echo "  $0 -v 2022              # Build only Windows Server 2022"
    echo "  $0 -p aws -v 2019 -d   # Build AWS 2019 with debug"
    echo "  $0 -j                   # Build all in parallel"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--platform)
            PLATFORM="$2"
            if [[ ! "$PLATFORM" =~ ^(vmware|aws|both)$ ]]; then
                print_color $RED "ERROR: Platform must be vmware, aws, or both"
                exit 1
            fi
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            if [[ ! "$VERSION" =~ ^(2019|2022|both)$ ]]; then
                print_color $RED "ERROR: Version must be 2019, 2022, or both"
                exit 1
            fi
            shift 2
            ;;
        -d|--debug)
            DEBUG_MODE=true
            shift
            ;;
        -j|--parallel)
            PARALLEL=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_color $RED "ERROR: Unknown option $1"
            show_usage
            exit 1
            ;;
    esac
done

print_color $GREEN "Automated Windows Server Image Builder"
print_color $GREEN "======================================"
print_color $CYAN "Platform: $PLATFORM"
print_color $CYAN "Version: $VERSION"
print_color $CYAN "Parallel: $PARALLEL"
echo ""

# Check prerequisites
print_color $YELLOW "Checking prerequisites..."

# Check if Packer is installed
if ! command -v packer &> /dev/null; then
    print_color $RED "ERROR: Packer is not installed or not in PATH"
    print_color $YELLOW "Please install Packer from: https://www.packer.io/downloads"
    exit 1
fi

PACKER_VERSION=$(packer version)
print_color $GREEN "✅ Packer installed: $PACKER_VERSION"

# Check platform-specific prerequisites
if [[ "$PLATFORM" == "vmware" || "$PLATFORM" == "both" ]]; then
    if [[ ! -f "variables/vmware.pkrvars.hcl" ]]; then
        print_color $RED "ERROR: VMware variable file 'variables/vmware.pkrvars.hcl' not found"
        exit 1
    fi
    print_color $GREEN "✅ VMware variables file found"
fi

if [[ "$PLATFORM" == "aws" || "$PLATFORM" == "both" ]]; then
    if [[ ! -f "variables/aws.pkrvars.hcl" ]]; then
        print_color $RED "ERROR: AWS variable file 'variables/aws.pkrvars.hcl' not found"
        exit 1
    fi
    print_color $GREEN "✅ AWS variables file found"
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        print_color $RED "ERROR: AWS credentials not configured"
        print_color $YELLOW "Please run: aws configure"
        exit 1
    fi
    print_color $GREEN "✅ AWS credentials configured"
fi

echo ""

# Set debug logging if requested
if [[ "$DEBUG_MODE" == true ]]; then
    export PACKER_LOG=1
    print_color $YELLOW "Debug logging enabled"
fi

# Function to run a build
run_build() {
    local platform=$1
    local version=$2
    
    print_color $CYAN "Starting $platform Windows Server $version build..."
    
    local start_time=$(date +%s)
    local success=false
    
    if [[ "$platform" == "vmware" ]]; then
        if ./build-vmware.sh -v "$version" $([ "$DEBUG_MODE" == true ] && echo "-d"); then
            success=true
        fi
    else
        if ./build-aws.sh -v "$version" $([ "$DEBUG_MODE" == true ] && echo "-d"); then
            success=true
        fi
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local hours=$((duration / 3600))
    local minutes=$(((duration % 3600) / 60))
    local seconds=$((duration % 60))
    
    if [[ "$success" == true ]]; then
        print_color $GREEN "$platform Windows Server $version build completed successfully!"
        printf "${CYAN}Duration: %02d:%02d:%02d${NC}\n" $hours $minutes $seconds
        return 0
    else
        print_color $RED "$platform Windows Server $version build failed!"
        return 1
    fi
}

# Generate build matrix
declare -a builds=()

if [[ "$PLATFORM" == "vmware" || "$PLATFORM" == "both" ]]; then
    if [[ "$VERSION" == "2019" || "$VERSION" == "both" ]]; then
        builds+=("vmware:2019")
    fi
    if [[ "$VERSION" == "2022" || "$VERSION" == "both" ]]; then
        builds+=("vmware:2022")
    fi
fi

if [[ "$PLATFORM" == "aws" || "$PLATFORM" == "both" ]]; then
    if [[ "$VERSION" == "2019" || "$VERSION" == "both" ]]; then
        builds+=("aws:2019")
    fi
    if [[ "$VERSION" == "2022" || "$VERSION" == "both" ]]; then
        builds+=("aws:2022")
    fi
fi

declare -a results=()

if [[ "$PARALLEL" == true && ${#builds[@]} -gt 1 ]]; then
    print_color $YELLOW "Running builds in parallel..."
    
    # Start background jobs
    declare -a pids=()
    for build in "${builds[@]}"; do
        IFS=':' read -r platform version <<< "$build"
        (
            if run_build "$platform" "$version"; then
                echo "$platform:$version:SUCCESS" > "/tmp/packer_result_${platform}_${version}.tmp"
            else
                echo "$platform:$version:FAILED" > "/tmp/packer_result_${platform}_${version}.tmp"
            fi
        ) &
        pids+=($!)
    done
    
    # Wait for all jobs to complete
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    # Collect results
    for build in "${builds[@]}"; do
        IFS=':' read -r platform version <<< "$build"
        if [[ -f "/tmp/packer_result_${platform}_${version}.tmp" ]]; then
            result=$(cat "/tmp/packer_result_${platform}_${version}.tmp")
            results+=("$result")
            rm -f "/tmp/packer_result_${platform}_${version}.tmp"
        else
            results+=("$platform:$version:FAILED")
        fi
    done
else
    print_color $YELLOW "Running builds sequentially..."
    
    for build in "${builds[@]}"; do
        IFS=':' read -r platform version <<< "$build"
        if run_build "$platform" "$version"; then
            results+=("$platform:$version:SUCCESS")
        else
            results+=("$platform:$version:FAILED")
        fi
        echo ""
    done
fi

# Display final results
echo ""
print_color $GREEN "========================================="
print_color $GREEN "FINAL BUILD SUMMARY"
print_color $GREEN "========================================="

success_count=0
total_count=${#results[@]}

for result in "${results[@]}"; do
    IFS=':' read -r platform version status <<< "$result"
    if [[ "$status" == "SUCCESS" ]]; then
        print_color $GREEN "✅ $platform Windows Server $version: $status"
        ((success_count++))
    else
        print_color $RED "❌ $platform Windows Server $version: $status"
    fi
done

echo ""
if [[ $success_count -eq $total_count ]]; then
    print_color $GREEN "🎉 All builds completed successfully! ($success_count/$total_count)"
    exit 0
else
    print_color $RED "⚠️  Some builds failed. ($success_count/$total_count successful)"
    exit 1
fi
