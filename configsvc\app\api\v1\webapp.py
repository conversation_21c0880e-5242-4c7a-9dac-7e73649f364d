from datetime import datetime, date, time
import json
# FastAPI modules
from typing import Annotated

from fastapi import APIRouter, Request, Form, Query, Depends, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel, Field
# from app.core.database import db<PERSON>heck

from app import logging
# from typing import date
from app.core.config import config

# For HTML pages
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
# from starlette.exceptions import HTTPException as StarletteHTTPException
from wtforms import <PERSON><PERSON>ield, FieldList, FormField, SelectField, IntegerField
from wtforms.validators import DataRequired
from starlette.datastructures import FormData, UploadFile

# Get Workers
from app.api.v1.workers import getAnnotations, getVSpecs

router = APIRouter()

templates = Jinja2Templates(directory="templates")

restart_time = datetime.now()
last_restart = str(restart_time).split(".")[0]
# dbstatus = dbCheck()

class AddResources(BaseModel):
    add_vcpus : int | None = 0
    add_vram : int | None = 0
    add_disk_gb : int | None = 0

server_data = []

@router.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    server_data = {"messages": []}    
    return templates.TemplateResponse("index.html", {"request": request, "server_data": server_data})

@router.post("/", response_class=HTMLResponse)
async def search_server(request: Request, server_name: str = Form(...)):
    # global count
    params = {"vm": server_name}
    if params['vm'] != "":
        server_data = getVSpecs(params) #getAnnotations(params)
        # print(json.dumps(server_data, indent=2))
        # add_resources = AddResources()

        return templates.TemplateResponse("server.html", 
                                        {"request": request, 
                                        "server_data": server_data})
    else:
        server_data = {"messages": ["Please specify server name..."]}        
        return templates.TemplateResponse("index.html", {"request": request, "server_data": server_data})

async def get_body(request: Request):
    content_type = request.headers.get('Content-Type')
    if content_type is None:
        raise HTTPException(status_code=400, detail='No Content-Type provided!')
    elif (content_type == 'application/x-www-form-urlencoded' or
          content_type.startswith('multipart/form-data')):
        try:
            return await request.form()
        except Exception:
            raise HTTPException(status_code=400, detail='Invalid Form data')
    else:
        raise HTTPException(status_code=400, detail='Content-Type not supported!')
