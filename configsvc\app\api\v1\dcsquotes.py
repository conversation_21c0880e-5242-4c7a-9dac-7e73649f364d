from datetime import datetime, date
import time
import os, json
# FastAPI modules
from typing import Annotated
import pandas as pd
import numpy as np
pd.options.display.float_format = "{:,.2f}".format
import ftplib

from fastapi import APIRouter, Request, Form, Query, Depends, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel, Field
# from app.core.database import dbCheck

from app import logging
# from typing import date
from app.core.config import config
from app.core.helpers import convert_int, ftp_get, ftp_post

# For HTML pages
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
# from starlette.exceptions import HTTPException as StarletteHTTPException
from wtforms import StringField, FieldList, FormField, SelectField, IntegerField
from wtforms.validators import DataRequired
from starlette.datastructures import FormData, UploadFile
# PDF Conversion
import subprocess
from docx.shared import Cm
from docxtpl import DocxTemplate #, InlineImage

# Get Workers
from app.api.v1.workers import getVSpecs

router = APIRouter()

templates = Jinja2Templates(directory="templates")

restart_time = datetime.now()
last_restart = str(restart_time).split(".")[0]
# dbstatus = dbCheck()

# Input request format
class AddResourcesQuoteRequest(BaseModel):
    reference: str | None = Field(default=None, examples=["DCS-999"])
    server_name: str = Field(default=None, examples=["SRV000000"])
    add_vcpus: int = Field(examples=[2])
    add_vram_gbs: int = Field(examples=[2])
    add_disk_gbs: str | None = Field(default=None, examples=["50,0,10"])


class stdResponse(BaseModel):
    status: str | None = None
    message: str | None = None
    data: str | None = None
    success: bool

templates_map = {
    "newserver": "dcs_new_servers.docx",
    "addresources": "dcs_addresources.docx"
}

disk_tags_map = {
    "NONE"          : "NON",
    "DIAMOND"       : "DIA",
    "SAPHIRE"       : "SAP",
    "QUARTZ"        : "QUA",
    "AMBER"         : "AMB",
    "RUBY"          : "RUB",
    "EMERALD"       : "EME"
}
# server_data = []

# Custom JSON encoder to handle datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime("%Y-%m-%d")  # Format the date as a string
        return super().default(obj)

# Update Quote template from source
@router.post("/updatetemplate",summary="Refresh template", tags=["dcsadmin"], response_model=stdResponse) 
async def postTemplates(request: Request):
    # global count
    params = await request.json()
    success = False
    if params:
        response = refreshTemplates(params)
    else:
        msg = (f"Error with api request: {params}")
        response =  {"success" : success, "message" : msg}
    logging.warning(msg)
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.get("/updatetemplate",summary="Refresh template", tags=["dcsadmin"], response_model=stdResponse) # 
async def getTemplates(request: Request):
    params = request.query_params
    logging.info(f"Template Update Request received: {params}")
    response = refreshTemplates(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))


async def get_body(request: Request):
    content_type = request.headers.get('Content-Type')
    if content_type is None:
        raise HTTPException(status_code=400, detail='No Content-Type provided!')
    elif (content_type == 'application/x-www-form-urlencoded' or
          content_type.startswith('multipart/form-data')):
        try:
            return await request.form()
        except Exception:
            raise HTTPException(status_code=400, detail='Invalid Form data')
    else:
        raise HTTPException(status_code=400, detail='Content-Type not supported!')


@router.post("/specs")
async def serverGetSpecs(request: Request):
    # global count
    params = await request.json()
    if params['vm'] != "":
        server_data = getVSpecs(params) #getAnnotations(params)
        return JSONResponse(content=jsonable_encoder(server_data)) #     "server_data": server_data})
    else:
        server_data = {"messages": ["Please specify server name..."]}        
        return JSONResponse(content=jsonable_encoder(server_data))

@router.post("/quote") #, response_class=HTMLResponse)
async def other(request: Request, body=Depends(get_body), background_tasks: BackgroundTasks = BackgroundTasks()):
    if isinstance(body, FormData):  # if Form/File data received
        print(f"Quote Request RAW:\n{body}")
        date_requested = datetime.now()
        server_name = body.get('server_name')
        print(f"server_name={server_name}")        
        reference = str(body.get('reference')).upper().replace(" ","_")
        print(f"reference={reference}")        
        vm_count = int(body.get('vm_count'))
        print(f"vm_count={vm_count}")
        add_vcpus = int(body.get('add_vcpus'))
        print(f"add_vcpus={add_vcpus}")
        add_vram_gbs = int(body.get('add_vram_gbs'))
        print(f"add_vrams={add_vram_gbs}")        
        add_srm = int(bool(body.get('add_srm')))
        print(f"add_srm={add_srm}")

        get_mirror_types = body.getlist('get_mirror_type')
        set_mirror_type = [str(item) for item in get_mirror_types]
        print(f"set_mirror_type={set_mirror_type}")

        add_disks = body.getlist('add_disk_gbs')
        add_disk_gbs = [int(item) for item in add_disks]
        print(f"add_disk_gbs={add_disk_gbs}")
        add_types = body.getlist('add_disk_types')
        add_disk_types = [str(item) for item in add_types]
        print(f"add_disk_types={add_disk_types}")
        # Make quote json
        quote_request = {
            "reference": reference, "date_requested": str(date_requested.date()), 
            "vm": server_name, "vm_count": vm_count, 
            "add_vcpus": add_vcpus, "add_vram_gbs": add_vram_gbs, "set_mirror_type": set_mirror_type,
            "add_disk_gbs": add_disk_gbs, "add_disk_types": add_disk_types,
            "add_srm": add_srm}
        logging.info(f"Quote request\n{quote_request}")
        print(f"Quote Request\n{json.dumps(quote_request, indent = 2)}\n")

        # quote_response = json.dumps(ProcessQuoteRequest(quote_request), cls=DateTimeEncoder)
        quote_response = ProcessQuoteRequest(quote_request)
        # print(f"Quote Response\n{json.dumps(quote_response, cls=DateTimeEncoder, indent = 2)}\n")
        logging.warning(f"Spawning background task to create pdf...")
        background_tasks.add_task(JsonToPdf, quote_response)

    return templates.TemplateResponse("show_quote.html", { "request": request, "quote_resp": quote_response["data"]})

# Convert to PDF in background
def JsonToPdf(srcDct):
    print("\nCONVERT JSON TO PDF\n")
    # time.sleep(3)
    srcFolder = config.DOCS_DIR
    template = srcDct['data']['template']
    tmplt_name = templates_map[template]
    dcstmplt = os.path.join(srcFolder, tmplt_name)
    print(f"TEMPLATE: {dcstmplt}\n")
    doc = DocxTemplate(dcstmplt)
    # wdFormatPDF = 17
    
    doc_name = srcDct['data']['docname'].upper()  +"_"+ template.upper()
    doc.render(srcDct['data'])
    doc_docx = os.path.join(srcFolder,doc_name+".docx")
    print(f"DOCX IN: {doc_docx}")
    #doc_docx=doc_name+".docx"
    doc.save(doc_docx)
    doc_pdf = doc_name+".pdf"
    doc_ftp = os.path.join(srcFolder, doc_pdf)
    print(f"PDF OUT: {doc_pdf}\n")
    data = {"filename": doc_pdf}
    convert2pdf = rf"soffice --headless --convert-to pdf {doc_docx} --outdir {srcFolder}" #rf"docx2pdf {doc_docx} {doc_pdf}"
    subprocess.run(convert2pdf, shell=True) 
    print(f"Create and Upload {doc_pdf} background task completed...\n")
    # return {"data" : "doc_pdf", "success": True}
    ftpFolder = config.DCS_PATH + "/quotes"
    ftpOK, msg = ftp_post(doc_ftp, doc_pdf, ftpFolder)
    logging.warning(msg)
    os.remove(doc_docx)
    os.remove(doc_ftp)
    logging.warning(f"Deleted local working files: {doc_docx}, {doc_ftp}")
    return {"success" : ftpOK, "message" : msg , "data": data}

    
    # return {"success": True, "filename": "filename"}


# GET quote2pdf payload
@router.get("/quote2pdf") #,summary="Receive Quote2Pdf (Quote)", tags=["dcsadmin"], response_model=stdResponse) # 
async def getQuote2Pdf(request: Request):
    """Process quote payload received on GET paramaters"""
    params = request.query_params
    # params['template'] = "addresources"
    logging.info(f"GET paramaters received:\n{params}")
    # response = uploadQuote(params)
    # response = params
    # return JSONResponse(content=jsonable_encoder(response))
    response = {"request": request, "server_data": server_data}
    server_data = ["Quote2Pdf request received!"]
    return templates.TemplateResponse("index.html", {"request": request, "server_data": server_data})


# POST body for quote2pdf payload
@router.post("/quote2pdf",summary="Receive Quote2Pdf (Quote)", tags=["dcsadmin"], response_model=stdResponse, response_class=HTMLResponse)  # 
async def postQuote2Pdf(request: Request):
    """Process quote payload received in POST Body"""
    params = await request.json() #json.loads(request.data)
    logging.info(f"POST payload body received: {params}")
    # response = convertToPdf(params)
    # response = {"params": params}
    # response = uploadQuote()
    # response = params
    # return JSONResponse(content=jsonable_encoder(response))
    # response = {"request": request, "server_data": server_data}
    server_data = ["Quote2Pdf request received!"]
    return templates.TemplateResponse("index.html", {"request": request, "server_data": server_data})

# Process Request
def ProcessQuoteRequest(dctIn):
    # names_issued = []
    messages = [] #server_data['messages']
    status = "NORMAL"
    success = True
    resp = {}
    server_data = getVSpecs(dctIn)
    messages = server_data['messages']
    if bool(server_data['success']):
        print("\nGet Server Specs:\n")
        annotations = server_data['data']['annotations']
        # print(server_data['data']['annotations'])
        decommed = annotations['decommissioned_ind.key']
        if decommed.lower() == "no":
            if bool(server_data['success']):
                df_quoteSpec = prepSpec(dctIn, server_data['data']['disks'])
                tmpquote = prepQuoteCosts(df_quoteSpec, server_data)
                # print(jsonable_encoder(server_data))   #, indent=2)}")        # logging.warning
                
                dctRes = df_quoteSpec.to_dict(orient='records')
                print("Quote Specs:")
                print(dctRes)
                quote_url = config.DOCS_DIR + str(dctIn["reference"]).upper()+"_ADDRESOURCES.pdf"
                print(f"Quote Generated: {quote_url}")
                quote_resp = {
                    "docname": dctIn["reference"],
                    "template": "addresources",
                    "quote_doc": config.DCS_QUOTES_URL + str(dctIn["reference"]).upper()+"_ADDRESOURCES.pdf",
                    "quote_request" : dctIn,
                    "quote_data": tmpquote,
                    "annotations" : annotations
                }
                # print(json.dumps(tmpquote, indent=2))
                # dctRsrv = dctIn.copy()
                resp.update({"success": success, "data" : quote_resp, "message": messages, "status": status})
            else:
                
                messages.append(f"{dctIn['vm']} must be quoted manually by DCS")
                quote_resp = {
                    "quote_request" : dctIn,
                    "quote_data": [{'quote_error': messages}],
                    "annotations" : annotations
                }
                status = "ERROR"
                resp.update({"success": False, "data" : quote_resp, "message": messages, "status": status})
        else:
            messages.append(f"{dctIn['vm']} is decommed")
            # f'{dctIn['vm']} is decommed'}
            quote_resp = {
                "quote_request" : dctIn,
                "quote_data": [{'quote_error': messages}],
                "annotations" : annotations
            }
            status = "ERROR"
            messages.append(f"{dctIn['vm']} is marked as decommissioned!")
            resp.update({"success": False, "data" : quote_resp, "message": messages, "status": status})
    else:        
        messages.append(f"If exists, {dctIn['vm']} must be quoted manually by DCS")
        quote_resp = {
            "quote_request" : dctIn,
            "quote_data": [{'quote_error': messages}],
            "annotations" : {}
        }
        status = "ERROR"
        resp.update({"success": False, "data" : quote_resp, "message": messages, "status": status})
    return resp

def prepSpec(dctIn, dctDisks):
    print("Prep Quote Spec:")
    # Adds resources column with items to quote against based on requested amounts
    dctSpec = { "vm_count": dctIn['vm_count'],"vcpus": dctIn['add_vcpus'], "vram": dctIn['add_vram_gbs'], "vmware_srm": dctIn['add_srm']} 
    for x in range (0, len(dctIn['add_disk_gbs']) ):
        dctSpec.update({"Hard disk "+str(x+1): dctIn['add_disk_gbs'][x] })
    if dctIn['set_mirror_type'][0].upper() == "NON-MIRRORED":
        mirror_tag = "NML" #dctSpec.update({"mirror_tag": "NML" })
    else:
        mirror_tag = "MSP"  #dctSpec.update({"mirror_tag": "MSP" })
    dctSpec.update({"backups": 0})
    print(f"{dctSpec}")
    dfOUT = pd.DataFrame(dctSpec.items(), columns=['resource','quantity'])
    # print("Without Disk and Mirror Tag")
    # print(f"{dfOUT}")
    dfOUT["disk_tag"] = ""
    dfOUT["mirror_tag"] = ""
    dfOUT["rates_tag"] = ""
    dfOUT.set_index('resource', inplace=True)
    for x in range (0, len(dctIn['add_disk_gbs']) ):
        dfOUT.loc[f"{'Hard disk '+str(x+1)}",'disk_tag'] = disk_tags_map[dctIn['add_disk_types'][x]]
        dfOUT.loc[f"{'Hard disk '+str(x+1)}",'mirror_tag'] = mirror_tag
    dfOUT.reset_index(inplace=True)
    print("With Disk and Mirror Tag")
    print(f"{dfOUT}")
    return dfOUT

# Calculate Base Costs:
def prepQuoteCosts(dfRequest, dctServer):
    print("\nPrep Quote Costs:\n")

    dctDisks = dctServer['data']['disks']
    dfDisks = pd.DataFrame.from_dict(dctDisks)
    print(f"Current Server Disks :")
    print(dfDisks[['disk','capacity_gb','disk_tag','mirror_tag','disk_type','mirror_type','disk_path']])

    disk_count = dfRequest["resource"].str.contains('disk').value_counts()[True]

    dfOUT = dfRequest.copy()
    dfRequest.set_index('resource', inplace=True)
    vm_count = dfRequest.loc['vm_count','quantity']

    print(f"\nFinal Disks Count: {disk_count}")
    print("\nAdd New Disk?")
    print(dfRequest.loc[f"Hard disk {str(disk_count)}"])
    print("Then Add to Current Disks...")
    # dfAddDisk = dfRequest.loc[f"Hard disk {str(disk_count)}"]
    dfAddDisk = dfOUT.loc[dfOUT['resource'] == f"Hard disk {str(disk_count)}"].copy()
    # dfAddDisk.reset_index(drop=True, inplace=True)
    print(f"\nDF Add Disk:\n{dfAddDisk}\n")
    # dfAddDisk = srsAddDisk.to_frame(name=f"Hard disk {str(disk_count)}")
    if dfAddDisk.iloc[0]['disk_tag'] != "NON":
        dfAddDisk.rename(columns={"resource": "disk", "quantity": "capacity_gb"}, inplace=True)
        # dfDisks.loc[len(dfDisks)]=dfAddDisk.loc[f"Hard disk {str(disk_count)}"] #dfRequest.loc[f"Hard disk {str(disk_count)}"]
        dfAddDisk["disk_path"] = "new disk"
        # dfAddDisk.drop(columns=['rates_tag'], inplace=True)
        print(f"ADD DISK: Type -> {type(dfAddDisk)}\n{dfAddDisk}")
        dfDisks = pd.concat([dfDisks, dfAddDisk])
        # dfDisks = dfDisks.append(dfAddDisk)
        dfDisks.reset_index(inplace=True)    
        print(f"After Adding Quote New Disk :")
    else:
        print(f"Skipping Quote for New Disk!")
    print(dfDisks[['disk','capacity_gb','disk_tag','mirror_tag','disk_type','mirror_type','disk_path']])

    dctRates = dctServer['data']['rates']
    dfRates = pd.DataFrame(dctRates.items(), columns=['resource','rate'])
    dfRates.set_index('resource', inplace=True)

    app_tier = dctServer['data']['annotations']['application_tier_code.label']
    print(f"\nApp Tier: {app_tier}")
    print("\nRates:")
    print(dfRates)
    print()
    # dfOUT.set_index('resource', inplace=True)
    dfOUT.set_index('resource', inplace=True)
    dfRequest.loc['total','resource'] = 0
    t2disks_total_gb, t3disks_total_gb = 0 , 0
    for d, row  in dfRequest.iterrows():
        resource = d # row['resource']
        print(f"ROW: {d}")
        if "vcpus" in d.lower(): #row['resource'].lower()):
            dfOUT.loc[d, 'rates_tag'] = resource.upper()
            dfOUT.loc[d, 'description'] = "vcpus"
            dfOUT.loc[d, 'rate'] = dfRates.at['vcpus','rate']
            dfOUT.loc[d, 'each'] = dfOUT.loc[d, 'quantity'] * dfOUT.loc[d, 'rate']
            dfOUT.loc[d, 'monthly'] = dfOUT.loc[d, 'quantity'] * dfOUT.loc[d, 'rate'] * vm_count
        if "vram" in d.lower(): #in row['resource'].lower()):
            dfOUT.loc[d, 'rates_tag'] = resource.upper()
            dfOUT.loc[d, 'description'] = "vram"
            dfOUT.loc[d, 'rate'] = dfRates.loc['vram','rate']
            dfOUT.loc[d, 'each'] = dfOUT.loc[d, 'quantity'] * dfOUT.loc[d, 'rate']
            dfOUT.loc[d, 'monthly'] = dfOUT.loc[d, 'quantity'] * dfOUT.loc[d, 'rate'] * vm_count
        if "srm" in d.lower(): #row['resource'].lower()):
            dfOUT.loc[d, 'rates_tag'] = "VMWARE_SRM"
            dfOUT.loc[d, 'description'] = "vmware_srm"
            dfOUT.loc[d, 'rate'] = dfRates.loc['vmware_srm','rate']            
            dfOUT.loc[d, 'each'] = dfOUT.loc[d, 'quantity'] * dfOUT.loc[d, 'rate']
            dfOUT.loc[d, 'monthly'] = dfOUT.loc[d, 'quantity'] * dfOUT.loc[d, 'rate'] * vm_count

        if "disk" in d.lower():          
            print(f"Disk -:> {d}") #  => {disk}
            for index, disk in dfDisks.iterrows():   
                sdisk = disk['disk'] #.lower() #.replace(" ","_")
                if d == sdisk:                       
                    disk_tier = "T2" if disk['disk_tag'].upper() in ["DIA","SAP"] else "T3"
                    disk_multiplier = 2 if disk['mirror_tag'].upper() in ["MSP","MSS"] else 1
                    disk_tag = disk_tier+"-"+disk['disk_tag']+"-"+disk['mirror_tag'] #+disk_multiplier #disk['disk_tag']+"-"+disk['mirror_tag']
                    print(f"Index = {d}, Disk = {d}, Disk_Tags = {disk_tag}") # row['resource']
                    dfOUT.loc[d, 'rates_tag'] = disk_tag
                    # determine which
                    rates_tag = "gold_disks" if disk['disk_tag'].upper() in ["DIA","SAP"] else "bronze_disks"
                    dfOUT.loc[d, 'description'] = rates_tag
                    dfOUT.loc[d, 'rate'] = dfRates.loc[rates_tag,'rate'] * disk_multiplier  #dfOUT.at['vm_count', 'quantity']
                    dfOUT.loc[d, 'each'] = dfOUT.loc[d, 'quantity'] * dfOUT.loc[d, 'rate']
                    if disk_tier == "T2":
                        t2disks_total_gb += dfOUT.loc[d, 'quantity']
                    else:
                        t3disks_total_gb += dfOUT.loc[d, 'quantity']
                    dfOUT.loc[d, 'monthly'] = dfOUT.loc[d, 'quantity'] * dfOUT.loc[d, 'rate'] * vm_count
                    dfOUT.loc[d, 'comment'] = disk['disk_path']
        if "vm_count" in d.lower(): #row['resource'].lower()):
            dfOUT.loc[d, 'each'] = vm_count / vm_count
            dfOUT.loc[d, 'rates_tag'] = "OS_INSTANCE"
            dfOUT.loc[d, 'rate'] = ""

    print(f"T2 Total Disks GB: {t2disks_total_gb}")
    print(f"T3 Total Disks GB: {t3disks_total_gb}\n")
    dfOUT.loc['backups', 'rate'] = dfRates.loc['backups','rate']
    if app_tier.upper() != "ATV":
        if "SQL" in dctServer['data']['compute']['cluster'].upper():
            dfOUT.loc['backups', 'each'] = t3disks_total_gb
            dfOUT.loc['backups', 'quantity'] = t3disks_total_gb
            backup_type = "BCKP-SQL"
        else:
            dfOUT.loc['backups', 'each'] = t2disks_total_gb + t3disks_total_gb
            dfOUT.loc['backups', 'quantity'] = t2disks_total_gb + t3disks_total_gb
            backup_type = "BCKP-PRD"
        dfOUT.loc['backups', 'rates_tag'] = backup_type
        dfOUT.loc['backups', 'description'] = "backups"        
        dfOUT.loc['backups', 'monthly'] = int(dfRates.loc['backups','rate'] * dfOUT.loc['backups', 'each'])
    # Clean Up Base Costs
    dfOUT.fillna(0, inplace=True)
    # Drop rows with no or zero value quantities
    dfOUT = dfOUT[dfOUT['quantity'] > 0]

    # Prep Monthly and Annual Costs
    quote_years = 3
    quote_months = 12
    today = datetime.now()
    year = today.year
    months_left = quote_months - today.month
    # Calculate monthly total
    dfOUT.loc['total', 'monthly'] = int(dfOUT['monthly'].sum(numeric_only = True)) # "{:.2f}".format(dfOUT['monthly'].sum(numeric_only = True))
    # Calculate BAU Years
    for x in range (1, quote_years+1):
        if x > 1:
            year += 1
            months_left = quote_months
        bau_year = "year"+str(x) #+" - "+str(year)
        dfOUT[bau_year] = dfOUT['monthly'] * months_left
        dfOUT[bau_year] = dfOUT[bau_year].apply(convert_int) #.astype(float)
        dfOUT.loc['total', bau_year] = dfOUT[bau_year].sum(numeric_only = True)
    # dfOUT.loc['total', 'monthly'] = dfOUT['monthly'].sum()
    dfOUT.reset_index(inplace=True)      # dfOUT 'resource',
    print(f"{dfOUT}\n")
    # Move disk comment column to last position in table
    last_column = dfOUT.pop('comment')
    dfOUT.insert(len(dfOUT.columns), 'comment', last_column)
    
    dfOUT.fillna("", inplace=True)
    dfOUT['description'].fillna("", inplace=True)
    
    print("Quote Dataframe:")
    print(f"{dfOUT}\n")
    # Make Dictionary
    dctRes = dfOUT.to_dict(orient='records')
    # print("Quote Dict:")
    # print(f"{dctRes}\n")

    return dctRes
    # return dfOUT

def refreshTemplates(params):
    filename = templates_map[params['template']]
    destination = os.path.join(config.DOCS_DIR,filename)
    ftpOK, ftpMsg = ftp_get(filename,config.DCS_PATH,destination)
    if ftpOK:
        msg = f"Template {filename} updated sucessfully..."
        # success =True
    else:
        msg = (f"Failed to download the template file -> {ftpMsg}")

    logging.warning(msg)
    return {"success" : ftpOK, "message" : msg}


def convertToPdf(srcDct):
    print("\nCONVERT TO PDF")
    data = json.loads(srcDct["data"])
    print(json.dumps(data['quote_request'], indent=2))
    srcFolder = config.DOCS_DIR
    # tmplt_name = templates_map[srcDct['data']['template']]
    tmplt_name = srcDct['data']['template']
    dcstmplt = os.path.join(srcFolder, tmplt_name)
    print(f"TEMPLATE: {dcstmplt}\n")
    doc = DocxTemplate(dcstmplt)
    wdFormatPDF = 17
    doc_ref = str(srcDct['data']['quote_request']['reference']).upper()
    doc_name = srcDct['template']+ "_" + doc_ref.strip()
    #print(json.dumps(context, indent=2),"\n")
    doc.render(srcDct['data'])
    doc_docx = os.path.join(srcFolder,doc_name+".docx")
    print(f"DOCX IN: {doc_docx}")
    #doc_docx=doc_name+".docx"
    doc.save(doc_docx)
    doc_pdf = doc_name+".pdf"
    doc_ftp = os.path.join(srcFolder, doc_pdf)
    print(f"PDF OUT: {doc_pdf}\n")
    data = {"filename": doc_pdf}
    # return {"data" : "doc_pdf", "success": True}
    ftpFolder = config.DCS_PATH + "/quotes"
    ftpOK, msg = ftp_post(doc_ftp, doc_pdf, ftpFolder)
    logging.warning(msg)
    return {"success" : ftpOK, "message" : msg , "data": data}

# Upload Quote as PDF
def uploadQuote(params):
    print("UPLOAD QUOTE:")
    filename = templates_map[params['template']]
    source = os.path.join(config.DOCS_DIR,filename)
    ftpFolder = config.DCS_PATH + "/quotes"
    ftpOK, msg = ftp_post(source, filename, ftpFolder)
    logging.warning(msg)
    return {"success" : ftpOK, "message" : msg}