# Windows Server 2022 Hyper-V Template for Local Testing
# This template allows you to test your Packer build process locally before deploying to VMware or AWS

packer {
  required_plugins {
    hyperv = {
      version = ">= 1.1.0"
      source  = "github.com/hashicorp/hyperv"
    }
  }
}

# Variables
variable "iso_path" {
  type        = string
  description = "Path to Windows Server 2022 ISO file"
  default     = "./ISO/Server 2022/SERVER_EVAL_x64FRE_en-us.iso"
}

variable "vm_name" {
  type        = string
  description = "VM name"
  default     = "windows-server-2022-local-test"
}

variable "vm_cpu_num" {
  type        = number
  description = "Number of CPUs"
  default     = 2
}

variable "vm_mem_size" {
  type        = number
  description = "Memory size in MB"
  default     = 4096
}

variable "vm_disk_size" {
  type        = number
  description = "Disk size in MB"
  default     = 61440  # 60GB
}

variable "winrm_username" {
  type        = string
  description = "WinRM username"
  default     = "Administrator"
}

variable "winrm_password" {
  type        = string
  description = "WinRM password"
  sensitive   = true
  default     = "P@ssw0rd123!"
}

variable "switch_name" {
  type        = string
  description = "Hyper-V virtual switch name"
  default     = "Default Switch"
}

# Source configuration
source "hyperv-iso" "windows-server-2022" {
  # ISO configuration
  iso_url      = var.iso_path
  iso_checksum = "none"  # Skip checksum for local testing
  
  # VM configuration
  vm_name              = var.vm_name
  generation           = 2
  cpus                 = var.vm_cpu_num
  memory               = var.vm_mem_size
  disk_size            = var.vm_disk_size
  switch_name          = var.switch_name
  enable_secure_boot   = false
  enable_virtualization_extensions = false
  
  # Floppy for unattended installation
  floppy_files = [
    "answer-files/windows-server-2022/autounattend.xml",
    "scripts/setup/enable-winrm.ps1"
  ]

  # Boot configuration
  boot_wait    = "10s"
  boot_command = ["<enter>"]

  # WinRM configuration
  communicator   = "winrm"
  winrm_username = var.winrm_username
  winrm_password = var.winrm_password
  winrm_timeout  = "30m"

  # Shutdown configuration
  shutdown_command = "shutdown /s /t 10 /f /d p:4:1 /c \"Packer Shutdown\""
  shutdown_timeout = "15m"
}

# Build configuration
build {
  sources = ["source.hyperv-iso.windows-server-2022"]

  # Wait for WinRM to be available
  provisioner "powershell" {
    inline = [
      "Write-Host 'WinRM is ready - starting local test build'",
      "Write-Host 'Testing Packer provisioning scripts locally'"
    ]
  }

  # Install Windows Updates (comment out for faster testing)
  provisioner "powershell" {
    script = "scripts/windows-updates.ps1"
  }

  # Configure Windows features and roles
  provisioner "powershell" {
    script = "scripts/configure-windows-features.ps1"
  }

  # Install common software
  provisioner "powershell" {
    script = "scripts/install-common-software.ps1"
  }

  # Security hardening
  provisioner "powershell" {
    script = "scripts/security-hardening.ps1"
  }

  # Final cleanup
  provisioner "powershell" {
    script = "scripts/cleanup.ps1"
  }

  # Local test completion message
  provisioner "powershell" {
    inline = [
      "Write-Host 'Local Hyper-V test build completed successfully!' -ForegroundColor Green",
      "Write-Host 'Scripts validated - ready for VMware/AWS deployment' -ForegroundColor Cyan"
    ]
  }
}
