#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
IMAGE_NAME="defirence680/iac-automation"
IMAGE_TAG="dev"
REGISTRY_URL="docker.io"

# --- Step 1: Build the Docker Image ---
echo "Building Docker image..."
docker build -f tf-pwsh.Dockerfile --secret id=TF_VAR_vsphere_credentials,src=TF_VAR_vsphere_credentials -t ${IMAGE_NAME}:${IMAGE_TAG} .

# --- Step 2: Tag and Push the Image to a Registry ---
echo "Tagging image for registry..."
docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG}

echo "Pushing image to registry..."
docker push ${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG}