# Packer Windows Server Image Builder

This directory contains the integrated Packer setup for building Windows Server images for both VMware vSphere and AWS environments.

## Overview

The Packer integration provides automated Windows Server image building capabilities using both PowerShell 7.5.2 and Linux shell scripts, all running within a containerized environment that includes:

- **Packer 1.10.0** - For image building
- **Terraform 1.12.2** - For infrastructure provisioning
- **PowerShell 7.5.2** - For Windows-specific automation
- **AWS CLI v2** - For AWS operations
- **Azure CLI** - For potential Azure builds

## Directory Structure

```
packer/
├── scripts/
│   ├── build/                    # Build automation scripts
│   │   ├── build-all.ps1        # PowerShell build orchestrator
│   │   ├── build-all.sh         # Linux build orchestrator
│   │   ├── build-aws.ps1        # AWS-specific PowerShell build
│   │   ├── build-aws.sh         # AWS-specific Linux build
│   │   ├── build-vmware.ps1     # VMware-specific PowerShell build
│   │   ├── build-vmware.sh      # VMware-specific Linux build
│   │   └── build-hyperv.ps1     # Hyper-V build (PowerShell only)
│   ├── setup/                   # Setup and configuration scripts
│   └── *.ps1                    # Individual PowerShell scripts
├── variables/                   # Packer variable files
│   ├── aws.pkrvars.hcl         # AWS-specific variables
│   ├── vmware.pkrvars.hcl      # VMware-specific variables
│   └── hyperv.pkrvars.hcl      # Hyper-V-specific variables
├── windows-server-2019/         # Windows Server 2019 templates
│   ├── aws.pkr.hcl             # AWS template
│   └── vmware.pkr.hcl          # VMware template
├── windows-server-2022/         # Windows Server 2022 templates
│   ├── aws.pkr.hcl             # AWS template
│   ├── vmware.pkr.hcl          # VMware template
│   └── hyperv.pkr.hcl          # Hyper-V template
├── answer-files/                # Windows unattended installation files
│   ├── windows-server-2019/    # 2019-specific answer files
│   └── windows-server-2022/    # 2022-specific answer files
├── logs/                        # Build logs (created at runtime)
└── ISO/                         # ISO storage (mount external directory)
```

## Usage

### Using Docker Compose

The Packer services are integrated into the main docker-compose.yml with profile support:

```bash
# Start all Packer services
docker compose --profile packer up -d

# Start only AWS builder
docker compose --profile aws-only up -d packer-aws

# Start only VMware builder  
docker compose --profile vmware-only up -d packer-vmware
```

### Build Commands

#### PowerShell Scripts (Recommended for Windows environments)

```bash
# Build all platforms and versions
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1

# Build specific platform
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Platform aws

# Build specific version
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Version 2022

# Build with debug mode
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -DebugMode

# Build in parallel
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Parallel
```

#### Linux Shell Scripts

```bash
# Build all platforms and versions
docker compose exec packer-builder packer/scripts/build/build-all.sh

# Build specific platform
docker compose exec packer-builder packer/scripts/build/build-all.sh --platform aws

# Build specific version
docker compose exec packer-builder packer/scripts/build/build-all.sh --version 2022

# Build with debug mode
docker compose exec packer-builder packer/scripts/build/build-all.sh --debug

# Build in parallel
docker compose exec packer-builder packer/scripts/build/build-all.sh --parallel
```

## Environment Variables

Configure the following environment variables in your `.env` file or docker-compose override:

### AWS Configuration
```
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=us-east-1
```

### VMware vSphere Configuration
```
VCENTER_SERVER=your_vcenter_server
VCENTER_USERNAME=your_username
VCENTER_PASSWORD=your_password
VCENTER_DATACENTER=your_datacenter
VCENTER_CLUSTER=your_cluster
VCENTER_DATASTORE=your_datastore
VCENTER_NETWORK=your_network
```

### Build Configuration
```
BUILD_PLATFORM=both          # vmware, aws, or both
BUILD_VERSION=both           # 2019, 2022, or both
BUILD_DEBUG=false            # Enable debug logging
BUILD_PARALLEL=false         # Enable parallel builds
PACKER_VERSION=1.10.0        # Packer version
```

## Prerequisites

1. **AWS Credentials**: Configure AWS credentials for AWS builds
2. **VMware Access**: Ensure network access to vCenter server
3. **ISO Files**: Mount Windows Server ISO files to the ISO directory
4. **Disk Space**: Ensure sufficient disk space for image builds (10GB+ recommended)

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure the container has proper permissions to access mounted volumes
2. **Network Issues**: Verify network connectivity to vCenter or AWS services
3. **ISO Not Found**: Ensure ISO files are properly mounted in the ISO directory
4. **Build Failures**: Check logs in the `packer/logs` directory

### Debug Mode

Enable debug mode for detailed logging:
```bash
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -DebugMode
```

### Logs

Build logs are stored in `packer/logs/packer.log` and can be monitored in real-time:
```bash
docker compose exec packer-builder tail -f packer/logs/packer.log
```

## Integration with IAC Automation

This Packer setup is fully integrated with the IAC automation pipeline and can be used in conjunction with:

- **Terraform**: For infrastructure provisioning
- **Node-RED**: For workflow automation
- **Windmill**: For job orchestration

The built images can be automatically deployed using the Terraform configurations in the main IAC automation workflow.
