{% extends "base.html" %}
{% block content %}
    <!-- <p class="text-danger"><b>{{ quote_data }}</b></p> -->

    <!-- Tabs navs  data-mdb-tab-init   -->

    <ul class="nav nav-tabs">
        <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#review_quote" aria-current="page"><b>Review Quote</b></a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#review_request" ><b>Requested Specs</b></a></li>
      </ul>
    <!-- Tabs navs -->

    <div class="tab-content" id="quote_requested">
        <p></p>
        <div class="tab-pane fade show active" id="review_quote" role="tabpanel" aria-labelledby="tab_review_quote">

            <p class="text-info"><b>Quoted Costs:</b></p>
            <table class="table">                    
                <thead>
                    <tr>
                        {% for key in quote_resp.quote_data[0].keys() %}
                            <th style="text-align: center;" scope="col"> {{ key }} </th>        
                        {% endfor %}  
                    </tr>
                </thead>
                <tbody>                
                {% for i in range (0, quote_resp.quote_data|length) %}
                <tr>
                    {% for key, value in quote_resp.quote_data[i].items() %}
                        <th style="text-align: center;" class="font-weight-light" scope="col"> {{ value }} </th>  
                    {% endfor %}
                </tr>
                {% endfor %}
                </tbody>                    
            </table>

            <p class="text-info"><b>Annotations:</b></p>
            <table class="table">            
                <tbody>
                    <tr> <td><b>Reference</b></td>  <td>{{ quote_resp.quote_request['reference']}}</td> <td><b>Date Requested</b></td>  <td>{{ quote_resp.quote_request['date_requested']}}</td> </tr>                        
                    <tr> <td><b>Server Name</b></td>  <td>{{ quote_resp.annotations['server_name'] }}</td> <td><b>Application</b></td>  <td>{{ quote_resp.annotations['application_name'] }}</td> </tr>
                    <tr> <td><b>Client</b></td>  <td>{{ quote_resp.annotations['client.label'] }}</td> <td><b>OS Competency</b></td>  <td>{{ quote_resp.annotations['competency_name.label'] }}</td> </tr>  
                    <tr> <td><b>App Owner</b></td>  <td>{{ quote_resp.annotations['fk_application_own.label'] }}</td> <td><b>SLA</b></td>  <td>{{ quote_resp.annotations['service_level_agreement_name.label'] }}</td> </tr>
                    <tr> <td><b>Tech Owner</b></td>  <td>{{ quote_resp.annotations['fk_primary_technical_owner.label'] }}</td> <td><b>Environment</b></td>  <td>{{ quote_resp.annotations['server_environment_name.label'] }}</td> </tr>
                    <tr> <td><b>Cost Centre</b></td>  <td>{{ quote_resp.annotations['fk_cost_centre.label'] }}</td>  <td><b>Location</b></td>  <td>{{ quote_resp.annotations['location_name.label'] }}</td> </tr>
                </tbody>                    
            </table>
        </div>

        <!-- Portrait view -->
        <div class="tab-pane fade" id="review_request" role="tabpanel" aria-labelledby="tab_review_request">

            <p class="text-info"><b>Requested:</b></p>
            <table class="table">                    
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>            
                    {% for key, value in quote_resp.quote_request.items() %}
                    <tr>
                        <td><b>{{ key }}</b></td>  <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>                    
            </table>
        </div>

        <a href="{{ quote_resp.quote_doc }}" class="navbar-brand" target="_blank">Download PDF
            <img src="/static/pdf.ico" height="28" alt="Download PDF">
        </a>
        <p></p>

    </div>
    <!-- Tabs content -->


{% endblock %}


   