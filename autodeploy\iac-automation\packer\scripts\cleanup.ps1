# Cleanup Script
# This script performs cleanup operations before finalizing the image

Write-Host "Starting cleanup operations..."

# Clear Windows Update cache
Write-Host "Clearing Windows Update cache..."
try {
    Stop-Service -Name "wuauserv" -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "C:\Windows\SoftwareDistribution\*" -Recurse -Force -ErrorAction SilentlyContinue
    Start-Service -Name "wuauserv" -ErrorAction SilentlyContinue
    Write-Host "Windows Update cache cleared."
} catch {
    Write-Host "Failed to clear Windows Update cache: $($_.Exception.Message)"
}

# Clear temporary files
Write-Host "Clearing temporary files..."
try {
    # Clear user temp files
    Remove-Item -Path "$env:TEMP\*" -Recurse -Force -ErrorAction SilentlyContinue
    
    # Clear system temp files
    Remove-Item -Path "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue
    
    # Clear Windows installer cache
    Remove-Item -Path "C:\Windows\Installer\$PatchCache$\*" -Recurse -Force -ErrorAction SilentlyContinue
    
    # Clear IIS logs if IIS is installed
    if (Get-WindowsFeature -Name "IIS-WebServer" | Where-Object { $_.InstallState -eq "Installed" }) {
        Remove-Item -Path "C:\inetpub\logs\LogFiles\*" -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    Write-Host "Temporary files cleared."
} catch {
    Write-Host "Failed to clear temporary files: $($_.Exception.Message)"
}

# Clear event logs
Write-Host "Clearing event logs..."
try {
    $eventLogs = @("Application", "Security", "System", "Setup")
    foreach ($log in $eventLogs) {
        wevtutil cl $log
        Write-Host "Cleared $log event log."
    }
} catch {
    Write-Host "Failed to clear event logs: $($_.Exception.Message)"
}

# Clear browser caches and data
Write-Host "Clearing browser data..."
try {
    # Clear Internet Explorer cache
    RunDll32.exe InetCpl.cpl,ClearMyTracksByProcess 8
    
    # Clear Chrome cache if installed
    $chromePath = "$env:LOCALAPPDATA\Google\Chrome\User Data\Default"
    if (Test-Path $chromePath) {
        Remove-Item -Path "$chromePath\Cache\*" -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item -Path "$chromePath\Cookies*" -Force -ErrorAction SilentlyContinue
        Remove-Item -Path "$chromePath\History*" -Force -ErrorAction SilentlyContinue
    }
    
    # Clear Firefox cache if installed
    $firefoxPath = "$env:APPDATA\Mozilla\Firefox\Profiles"
    if (Test-Path $firefoxPath) {
        Get-ChildItem -Path $firefoxPath | ForEach-Object {
            Remove-Item -Path "$($_.FullName)\cache2\*" -Recurse -Force -ErrorAction SilentlyContinue
            Remove-Item -Path "$($_.FullName)\cookies.sqlite" -Force -ErrorAction SilentlyContinue
            Remove-Item -Path "$($_.FullName)\places.sqlite" -Force -ErrorAction SilentlyContinue
        }
    }
    
    Write-Host "Browser data cleared."
} catch {
    Write-Host "Failed to clear browser data: $($_.Exception.Message)"
}

# Clear Windows Search index
Write-Host "Clearing Windows Search index..."
try {
    Stop-Service -Name "WSearch" -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "C:\ProgramData\Microsoft\Search\Data\*" -Recurse -Force -ErrorAction SilentlyContinue
    Start-Service -Name "WSearch" -ErrorAction SilentlyContinue
    Write-Host "Windows Search index cleared."
} catch {
    Write-Host "Failed to clear Windows Search index: $($_.Exception.Message)"
}

# Clear PowerShell history
Write-Host "Clearing PowerShell history..."
try {
    Remove-Item -Path "$env:APPDATA\Microsoft\Windows\PowerShell\PSReadLine\ConsoleHost_history.txt" -Force -ErrorAction SilentlyContinue
    Write-Host "PowerShell history cleared."
} catch {
    Write-Host "Failed to clear PowerShell history: $($_.Exception.Message)"
}

# Clear Windows Defender scan history
Write-Host "Clearing Windows Defender history..."
try {
    Remove-Item -Path "C:\ProgramData\Microsoft\Windows Defender\Scans\History\*" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Windows Defender history cleared."
} catch {
    Write-Host "Failed to clear Windows Defender history: $($_.Exception.Message)"
}

# Clear registry entries that might contain sensitive information
Write-Host "Clearing sensitive registry entries..."
try {
    # Clear recent documents
    Remove-Item -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\RecentDocs\*" -Force -ErrorAction SilentlyContinue
    
    # Clear run history
    Remove-Item -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\RunMRU\*" -Force -ErrorAction SilentlyContinue
    
    # Clear typed URLs
    Remove-Item -Path "HKCU:\Software\Microsoft\Internet Explorer\TypedURLs\*" -Force -ErrorAction SilentlyContinue
    
    Write-Host "Sensitive registry entries cleared."
} catch {
    Write-Host "Failed to clear registry entries: $($_.Exception.Message)"
}

# Defragment the system drive
Write-Host "Defragmenting system drive..."
try {
    defrag C: /O
    Write-Host "System drive defragmented."
} catch {
    Write-Host "Failed to defragment system drive: $($_.Exception.Message)"
}

# Clear DNS cache
Write-Host "Clearing DNS cache..."
try {
    ipconfig /flushdns
    Write-Host "DNS cache cleared."
} catch {
    Write-Host "Failed to clear DNS cache: $($_.Exception.Message)"
}

# Clear ARP cache
Write-Host "Clearing ARP cache..."
try {
    arp -d *
    Write-Host "ARP cache cleared."
} catch {
    Write-Host "Failed to clear ARP cache: $($_.Exception.Message)"
}

# Clear NetBIOS cache
Write-Host "Clearing NetBIOS cache..."
try {
    nbtstat -R
    Write-Host "NetBIOS cache cleared."
} catch {
    Write-Host "Failed to clear NetBIOS cache: $($_.Exception.Message)"
}

# Remove Windows.old folder if it exists
Write-Host "Removing Windows.old folder..."
try {
    if (Test-Path "C:\Windows.old") {
        Remove-Item -Path "C:\Windows.old" -Recurse -Force
        Write-Host "Windows.old folder removed."
    } else {
        Write-Host "Windows.old folder not found."
    }
} catch {
    Write-Host "Failed to remove Windows.old folder: $($_.Exception.Message)"
}

# Run Disk Cleanup
Write-Host "Running Disk Cleanup..."
try {
    # Create cleanmgr settings for automated cleanup
    $cleanmgrKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\VolumeCaches"
    $volumeCaches = @(
        "Active Setup Temp Folders",
        "BranchCache",
        "Downloaded Program Files",
        "Internet Cache Files",
        "Offline Pages Files",
        "Old ChkDsk Files",
        "Previous Installations",
        "Recycle Bin",
        "Setup Log Files",
        "System error memory dump files",
        "System error minidump files",
        "Temporary Files",
        "Temporary Setup Files",
        "Thumbnail Cache",
        "Update Cleanup",
        "Windows Defender",
        "Windows Error Reporting Archive Files",
        "Windows Error Reporting Queue Files",
        "Windows Error Reporting System Archive Files",
        "Windows Error Reporting System Queue Files",
        "Windows ESD installation files",
        "Windows Upgrade Log Files"
    )
    
    foreach ($cache in $volumeCaches) {
        $cachePath = "$cleanmgrKey\$cache"
        if (Test-Path $cachePath) {
            Set-ItemProperty -Path $cachePath -Name "StateFlags0001" -Value 2
        }
    }
    
    # Run cleanmgr
    Start-Process -FilePath "cleanmgr.exe" -ArgumentList "/sagerun:1" -Wait
    
    Write-Host "Disk Cleanup completed."
} catch {
    Write-Host "Failed to run Disk Cleanup: $($_.Exception.Message)"
}

# Zero out free space (optional - uncomment if needed for security)
# Write-Host "Zeroing out free space..."
# try {
#     sdelete -z -accepteula C:
#     Write-Host "Free space zeroed out."
# } catch {
#     Write-Host "Failed to zero out free space: $($_.Exception.Message)"
# }

Write-Host "Cleanup operations completed."
