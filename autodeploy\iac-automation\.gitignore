# Created by https://www.toptal.com/developers/gitignore/api/terraform
# Edit at https://www.toptal.com/developers/gitignore?templates=terraform

### Terraform ###
# Local .terraform directories
./terraform
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Lock file (required on non-local deployments
.terraform.lock.hcl

# Terraform plan file(s)
tfplan

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data, such as
# password, private keys, and other secrets. These should not be part of version
# control as they are resource points which are potentially sensitive and subject
# to change depending on the environment.
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
example: *tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore dotenv files
.env
*.env

terraform.tfvars.backup