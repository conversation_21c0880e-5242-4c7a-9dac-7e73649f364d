# Hyper-V Local Testing Variables
# Update these values to match your local environment

# ISO path (relative to the packer directory)
iso_path = "./ISO/Server 2022/SERVER_EVAL_x64FRE_en-us.iso"

# VM specifications for local testing
vm_cpu_num  = 2
vm_mem_size = 4096  # 4GB in MB
vm_disk_size = 61440  # 60GB in MB

# WinRM credentials (used during build)
winrm_username = "Administrator"
winrm_password = "P@ssw0rd123!"  # Change this to a secure password

# Hyper-V configuration
switch_name = "Default Switch"  # Use your Hyper-V switch name

# VM naming
vm_name = "windows-server-2022-local-test"
