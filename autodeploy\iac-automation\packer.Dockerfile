# Packer Build Container for Windows Server Images
# This container provides a consistent environment for running <PERSON><PERSON> builds
FROM ubuntu:22.04

# Build arguments
ARG PACKER_VERSION=1.10.0
ARG TERRAFORM_VERSION=1.6.6

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PACKER_VERSION=${PACKER_VERSION}
ENV TERRAFORM_VERSION=${TERRAFORM_VERSION}
ENV AWS_CLI_VERSION=2.15.0

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    git \
    jq \
    python3 \
    python3-pip \
    openssh-client \
    ca-certificates \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# Install Packer
RUN wget -O packer.zip "https://releases.hashicorp.com/packer/${PACKER_VERSION}/packer_${PACKER_VERSION}_linux_amd64.zip" \
    && unzip packer.zip \
    && mv packer /usr/local/bin/ \
    && rm packer.zip \
    && chmod +x /usr/local/bin/packer

# Install Terraform (useful for infrastructure provisioning)
RUN wget -O terraform.zip "https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip" \
    && unzip terraform.zip \
    && mv terraform /usr/local/bin/ \
    && rm terraform.zip \
    && chmod +x /usr/local/bin/terraform

# Install AWS CLI v2
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -rf aws awscliv2.zip

# Install Azure CLI (for potential Azure builds)
RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash

# Create packer user and workspace
RUN useradd -m -s /bin/bash packer \
    && mkdir -p /workspace \
    && chown -R packer:packer /workspace

# Set working directory
WORKDIR /workspace

# Switch to packer user
USER packer

# Verify installations
RUN packer version \
    && terraform version \
    && aws --version \
    && az version

# Default command
CMD ["/bin/bash"]

# Labels for metadata
LABEL maintainer="AutoDeploy Team"
LABEL description="Packer build environment for Windows Server images"
LABEL version="1.0"
